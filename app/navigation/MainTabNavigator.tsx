import React, { useRef, useState, useEffect } from 'react';
import { View, Platform, Animated } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../theme/typography';
import { useTheme } from '../theme/ThemeProvider';
import { NavigatorScreenParams, ParamListBase } from '@react-navigation/native';
import { useNavigation, useIsFocused } from '@react-navigation/native';

// Import screens
import LotusScreen from '../screens/LotusScreen';
import StrengthScreen from '../screens/StrengthScreen';
import DietScreen from '../screens/DietScreen';
import DigestScreen from '../screens/DigestScreen'; // Added
import ProfileStackNavigator from './ProfileStackNavigator';

// Define navigation types
export type MainTabParamList = {
  Lotus: { conversationId?: string } | undefined;
  Strength: undefined;
  Diet: undefined;
  Digest: undefined; // Added
  Profile: NavigatorScreenParams<ProfileStackParamList> | undefined;
};

export type ProfileStackParamList = {
  ProfileMain: undefined;
  EditProfile: undefined;
  PrivacySettings: undefined;
  HelpSupport: undefined;
};

const Tab = createBottomTabNavigator<MainTabParamList>();

export default function MainTabNavigator() {
  const { colors } = useTheme();
  const navigation = useNavigation();

  // Preload all screens to prevent any layout shifts
  const [screensPreloaded, setScreensPreloaded] = useState(false);

  useEffect(() => {
    // Enable preloaded state after initial render to ensure all screens are ready
    const timer = setTimeout(() => {
      setScreensPreloaded(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  // Simple function to handle any additional logic needed when tabs are pressed
  // No animation or transition handling needed
  const handleTabPress = (targetTab: string) => () => {
    // This function is now a no-op, but we keep it in case we need to add logic later
    // No need to prevent default navigation or manage animations
  };

  return (
    <View style={{
      flex: 1,
      backgroundColor: colors.background
    }}>
      <Tab.Navigator
        screenOptions={({ route }) => ({
          tabBarIcon: ({ focused, color }) => {
            let iconName;
            // Use a larger icon size since we removed the labels
            const iconSize = 26;

            switch (route.name) {
              case 'Lotus':
                iconName = focused ? 'chatbubbles' : 'chatbubbles-outline';
                break;
              case 'Strength':
                iconName = focused ? 'barbell' : 'barbell-outline';
                break;
              case 'Diet':
                iconName = focused ? 'restaurant' : 'restaurant-outline';
                break;
              case 'Digest': // Added
                iconName = focused ? 'newspaper' : 'newspaper-outline'; // Example icon
                break;
              case 'Profile':
                iconName = focused ? 'person' : 'person-outline';
                break;
              default:
                iconName = 'alert';
            }

            return <Ionicons name={iconName as any} size={iconSize} color={color} />;
          },
          // Force all tabs to be pre-rendered and always mounted
          lazy: false,
          detachInactiveScreens: false,
          freezeOnBlur: false,

          // Enable built-in animations since we're not using custom fade anymore
          animationEnabled: true,

          tabBarActiveTintColor: colors.primary,
          tabBarInactiveTintColor: colors.textSecondary,
          tabBarShowLabel: false, // Remove text labels
          tabBarStyle: {
            backgroundColor: colors.background,
            borderTopColor: colors.border,
            paddingBottom: Platform.OS === 'ios' ? 2 : 0, // Minimal bottom padding
            paddingTop: 8,
            height: Platform.OS === 'ios' ? 50 : 45, // Further reduced height
            elevation: 5, // Add shadow on Android
            shadowOpacity: 0.1, // Add shadow on iOS
            shadowOffset: { height: -2, width: 0 },
            shadowRadius: 3,
            position: 'absolute', // Position at bottom
            bottom: 0,
            zIndex: 100, // Ensure it's above other elements
          },
          tabBarIconStyle: {
            marginTop: 0,
            marginBottom: Platform.OS === 'ios' ? 10 : 8, // Push icons toward bottom
          },
          headerShown: false,
        })}
      >
        <Tab.Screen
          name="Lotus"
          component={LotusScreen}
          // No custom listeners needed for standard navigation
          options={{
            title: 'Lotus',
            unmountOnBlur: false,
          }}
        />
        <Tab.Screen
          name="Strength"
          component={StrengthScreen}
          // No custom listeners needed for standard navigation
          options={{
            title: 'Strength',
            unmountOnBlur: false,
          }}
        />
        <Tab.Screen
          name="Diet"
          component={DietScreen}
          // No custom listeners needed for standard navigation
          options={{
            title: 'Diet',
            unmountOnBlur: false,
          }}
        />
        <Tab.Screen
          name="Digest" // Added
          component={DigestScreen}
          options={{
            title: 'Digest',
            unmountOnBlur: false,
          }}
        />
        <Tab.Screen
          name="Profile"
          component={ProfileStackNavigator}
          // No custom listeners needed for standard navigation
          options={{
            title: 'Profile',
            // animationEnabled: true, // Removed invalid option
            unmountOnBlur: false,
            lazy: false,
          }}
        />
      </Tab.Navigator>
    </View>
  );
}
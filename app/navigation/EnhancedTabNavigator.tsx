import React from 'react';
import { View, Platform, TouchableOpacity, Text } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useTheme } from '../theme/ThemeProvider';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring, 
  withTiming,
  interpolate,
  Easing
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import CustomIcon, { IconName } from '../components/icons/CustomIcon';
import { AnimatedInteractiveView } from '../components/animations/AnimationSystem';

// Import screens
import LotusScreen from '../screens/LotusScreen';
import StrengthScreen from '../screens/StrengthScreen';
import DietScreen from '../screens/DietScreen';
import DigestScreen from '../screens/DigestScreen';
import ProfileStackNavigator from './ProfileStackNavigator';

export type MainTabParamList = {
  Lotus: { conversationId?: string } | undefined;
  Strength: undefined;
  Diet: undefined;
  Digest: undefined;
  Profile: undefined;
};

const Tab = createBottomTabNavigator<MainTabParamList>();

// Enhanced Tab Bar Icon Component
interface TabBarIconProps {
  focused: boolean;
  color: string;
  size: number;
  iconName: IconName;
  route: string;
}

const TabBarIcon: React.FC<TabBarIconProps> = ({ focused, color, size, iconName, route }) => {
  const scale = useSharedValue(focused ? 1 : 0.8);
  const opacity = useSharedValue(focused ? 1 : 0.7);
  const glowOpacity = useSharedValue(0);
  const translateY = useSharedValue(0);

  React.useEffect(() => {
    scale.value = withSpring(focused ? 1.1 : 0.9, {
      damping: 15,
      stiffness: 200,
      mass: 0.8
    });
    
    opacity.value = withTiming(focused ? 1 : 0.6, {
      duration: 200,
      easing: Easing.out(Easing.cubic)
    });

    translateY.value = withSpring(focused ? -2 : 0, {
      damping: 12,
      stiffness: 150
    });

    if (focused) {
      glowOpacity.value = withTiming(0.3, { duration: 300 });
      // Add haptic feedback when tab becomes focused
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } else {
      glowOpacity.value = withTiming(0, { duration: 200 });
    }
  }, [focused]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { translateY: translateY.value }
    ],
    opacity: opacity.value,
  }));

  const glowStyle = useAnimatedStyle(() => ({
    position: 'absolute',
    top: -4,
    left: -4,
    right: -4,
    bottom: -4,
    borderRadius: 20,
    backgroundColor: color,
    opacity: glowOpacity.value,
    zIndex: -1,
  }));

  return (
    <View style={{ 
      position: 'relative', 
      alignItems: 'center', 
      justifyContent: 'center',
      width: 40,
      height: 40
    }}>
      <Animated.View style={glowStyle} />
      <Animated.View style={animatedStyle}>
        <CustomIcon
          name={iconName}
          size={size}
          color={color}
          animated={true}
          scaleOnPress={false} // Handled by tab animation
          pulseEffect={focused}
          glowEffect={focused}
          glowColor={color}
        />
      </Animated.View>
    </View>
  );
};

// Custom Tab Bar Component
interface CustomTabBarProps {
  state: any;
  descriptors: any;
  navigation: any;
}

const CustomTabBar: React.FC<CustomTabBarProps> = ({ state, descriptors, navigation }) => {
  const { colors, isDark } = useTheme();

  const getIconName = (routeName: string, focused: boolean): IconName => {
    switch (routeName) {
      case 'Lotus':
        return 'lotus-chat';
      case 'Strength':
        return 'lotus-strength';
      case 'Diet':
        return 'lotus-nutrition';
      case 'Digest':
        return 'lotus-calendar';
      case 'Profile':
        return 'lotus-profile';
      default:
        return 'lotus-chat';
    }
  };

  return (
    <View style={{
      flexDirection: 'row',
      backgroundColor: colors.background,
      borderTopColor: colors.border,
      borderTopWidth: 1,
      paddingBottom: Platform.OS === 'ios' ? 20 : 10,
      paddingTop: 12,
      paddingHorizontal: 20,
      elevation: 8,
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowOffset: { height: -2, width: 0 },
      shadowRadius: 8,
      shadowColor: colors.text,
    }}>
      {state.routes.map((route: any, index: number) => {
        const { options } = descriptors[route.key];
        const isFocused = state.index === index;

        const onPress = () => {
          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name);
          }
        };

        const onLongPress = () => {
          navigation.emit({
            type: 'tabLongPress',
            target: route.key,
          });
        };

        return (
          <AnimatedInteractiveView
            key={route.key}
            style={{ flex: 1, alignItems: 'center' }}
            onPress={onPress}
            onLongPress={onLongPress}
            hapticFeedback="light"
            scaleOnPress={true}
            pressScale={0.9}
            animationPreset="snappy"
          >
            <TabBarIcon
              focused={isFocused}
              color={isFocused ? colors.primary : colors.textSecondary}
              size={26}
              iconName={getIconName(route.name, isFocused)}
              route={route.name}
            />
          </AnimatedInteractiveView>
        );
      })}
    </View>
  );
};

export default function EnhancedTabNavigator() {
  const { colors } = useTheme();

  return (
    <View style={{
      flex: 1,
      backgroundColor: colors.background
    }}>
      <Tab.Navigator
        tabBar={(props) => <CustomTabBar {...props} />}
        screenOptions={{
          headerShown: false,
          lazy: false,
          detachInactiveScreens: false,
          freezeOnBlur: false,
          animationEnabled: true,
        }}
      >
        <Tab.Screen
          name="Lotus"
          component={LotusScreen}
          options={{
            title: 'Lotus',
            unmountOnBlur: false,
          }}
        />
        <Tab.Screen
          name="Strength"
          component={StrengthScreen}
          options={{
            title: 'Strength',
            unmountOnBlur: false,
          }}
        />
        <Tab.Screen
          name="Diet"
          component={DietScreen}
          options={{
            title: 'Diet',
            unmountOnBlur: false,
          }}
        />
        <Tab.Screen
          name="Digest"
          component={DigestScreen}
          options={{
            title: 'Digest',
            unmountOnBlur: false,
          }}
        />
        <Tab.Screen
          name="Profile"
          component={ProfileStackNavigator}
          options={{
            title: 'Profile',
            unmountOnBlur: false,
            lazy: false,
          }}
        />
      </Tab.Navigator>
    </View>
  );
}

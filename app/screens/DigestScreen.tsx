import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  Animated as RNAnimated,
  PanResponder,
  Dimensions,
  ActivityIndicator,
  Alert,
  Platform,
  FlatList,
  RefreshControl,
  ScrollView,
} from 'react-native';
import { useTheme } from '../theme/ThemeProvider';
import { typography } from '../theme/typography';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import type { MainTabParamList } from '../navigation/MainTabNavigator';
import DailyTimeline from '../components/digest/DailyTimeline';
import DigestCalendar from '../components/digest/DigestCalendar';
import ActivityEditModal from '../components/digest/ActivityEditModal';
import DigestContextModal from '../components/digest/DigestContextModal';
import MealDetailModal from '../components/digest/MealDetailModal';
import WorkoutDetailModal from '../components/digest/WorkoutDetailModal';
import { DigestActivity, DigestActivityType, getDayDigest, saveDigestToAPI, generateBasicActivities, createDigestOnAPI } from '../services/digestService';
import { getApiUrl } from '../services/apiClient';
import { getAuthHeader } from '../services/auth';
import { format, addDays, subDays } from 'date-fns';
import TimeDisplay from '../components/TimeDisplay';
import MiniWeatherDisplay from '../components/MiniWeatherDisplay';
import { getCurrentWeather, WeatherData } from '../services/nutritionService';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useWeather } from '../context/WeatherContext';
import { debugTokens, testApiEndpoints, testDigestRegenerate } from '../utils/tokenDebug';
import Header from '../components/common/Header';

// Import ActivityItem directly with require to avoid TypeScript errors
const ActivityItem = require('../../app/components/digest/ActivityItem').default;

const DigestScreen: React.FC = () => {
  const { colors } = useTheme();
  const navigation = useNavigation<BottomTabNavigationProp<MainTabParamList>>();
  const insets = useSafeAreaInsets();
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showCalendar, setShowCalendar] = useState(false);
  const [selectedActivity, setSelectedActivity] = useState<DigestActivity | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showContextModal, setShowContextModal] = useState(false);
  const [showResetModal, setShowResetModal] = useState(false);
  const [showMealModal, setShowMealModal] = useState(false);
  const [showWorkoutModal, setShowWorkoutModal] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [refreshError, setRefreshError] = useState<string | null>(null);
  const [dayDigest, setDayDigest] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  // New state for redesigned interface
  const [activeTab, setActiveTab] = useState<'today' | 'calendar' | 'insights'>('today');

  const screenWidth = Dimensions.get('window').width;
  const position = useRef(new RNAnimated.Value(0)).current;
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    const loadInitialData = async () => {
      setIsLoading(true);

      try {
        const dateStr = format(selectedDate, 'yyyy-MM-dd');

        // Try to get existing digest for today
        try {
          const result = await getDayDigest(selectedDate);
          if (result && result.activities && result.activities.length > 0) {
            setDayDigest(result);
            setIsLoading(false);
            return;
          }
        } catch (error) {
          console.log('No existing digest found, will generate a new one');
        }

        // If we get here, we need to generate a new digest
        refreshDigest();
      } catch (error) {
        console.error('Error during initial digest load:', error);
        setIsLoading(false);
      }
    };

    loadInitialData();
  }, []);

  // Add effect to reload digest when date changes
  useEffect(() => {
    const loadDigestForDate = async () => {
      setIsLoading(true);
      try {
        const result = await getDayDigest(selectedDate);
        setDayDigest(result);
      } catch (error) {
        console.error('Error loading digest for date:', error);
        setRefreshError('Failed to load digest for selected date');
      } finally {
        setIsLoading(false);
      }
    };

    loadDigestForDate();
  }, [selectedDate]);



  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (_, gestureState) => {
        return Math.abs(gestureState.dx) > 20;
      },
      onPanResponderMove: (_, gestureState) => {
        position.setValue(gestureState.dx);
      },
      onPanResponderRelease: (_, gestureState) => {
        if (gestureState.dx > 50) {
          handlePreviousDay();
        } else if (gestureState.dx < -50) {
          handleNextDay();
        }
        RNAnimated.spring(position, {
          toValue: 0,
          useNativeDriver: true,
        }).start();
      },
    })
  ).current;

  const handlePreviousDay = () => {
    setSelectedDate(prev => subDays(prev, 1));
  };

  const handleNextDay = () => {
    setSelectedDate(prev => addDays(prev, 1));
  };

  const handleOpenCalendar = () => {
    setShowCalendar(true);
  };

  const handleSelectDate = (date: Date) => {
    setSelectedDate(date);
    setShowCalendar(false);
  };

  const handleEditActivity = (activity: DigestActivity) => {
    setSelectedActivity(activity);
    setShowEditModal(true);
  };

  const handleSaveActivity = () => {
    setSelectedActivity(null);
    setShowEditModal(false);
  };

  const handleSelectActivity = (activity: DigestActivity) => {
    setSelectedActivity(activity);

    if (activity.type === DigestActivityType.MEAL) {
      setShowMealModal(true);
    } else if (activity.type === DigestActivityType.WORKOUT) {
      setShowWorkoutModal(true);
    }
  };

  const handleAddContext = () => {
    setShowContextModal(true);
  };

  const handleResetFuturePlans = () => {
    setShowResetModal(true);
  };

  const handleContextSuccess = () => {
    setShowContextModal(false);
    setShowResetModal(false);
    refreshDigest();
  };

  const handleToggleComplete = (activityId: string) => {
    if (!dayDigest || !dayDigest.activities) return;
    
    const updatedActivities = dayDigest.activities.map((activity: any) => {
      if (activity.id === activityId) {
        return {
          ...activity,
          completed: !activity.completed
        };
      }
      return activity;
    });
    
    setDayDigest({
      ...dayDigest,
      activities: updatedActivities
    });
    
    try {
      const { saveDigestToAPI } = require('../services/digestService');
      const dateStr = format(selectedDate, 'yyyy-MM-dd');
      saveDigestToAPI(dateStr, {
        ...dayDigest,
        activities: updatedActivities
      });
    } catch (error) {
      console.error('Error saving activity completion status:', error);
    }
  };

  const handleViewDetails = (activityId: string) => {
    if (!dayDigest || !dayDigest.activities) return;
    const activity = dayDigest.activities.find((a: any) => a.id === activityId);
    if (activity) {
      setSelectedActivity(activity);
      
      if (activity.type.toLowerCase() === 'meal') {
        setShowMealModal(true);
      } else if (activity.type.toLowerCase() === 'workout') {
        setShowWorkoutModal(true);
      } else {
        Alert.alert(
          activity.title,
          activity.description,
          [{ text: 'OK', onPress: () => console.log('OK Pressed') }]
        );
      }
    }
  };

  const refreshDigest = async () => {
    setIsRefreshing(true);
    setRefreshError(null);

    try {
      const dateStr = format(selectedDate, 'yyyy-MM-dd');
      
      console.log(`[DigestScreen] Refreshing digest for ${dateStr}`);
      
      // First, let's check what context data is available before calling the API
      console.log('[DigestScreen] Checking available context before API call...');
      try {
        const { getContextData, ContextType } = require('../services/contextService');
        const preferenceContext = await getContextData(ContextType.PREFERENCE);
        const customContext = await getContextData(ContextType.CUSTOM);
        
        console.log('[DigestScreen] PREFERENCE context entries:', preferenceContext.length);
        console.log('[DigestScreen] CUSTOM context entries:', customContext.length);
        
        const dailyPrefs = preferenceContext.find((item: any) => 
          item.metadata?.category === 'daily_preferences' && 
          item.metadata?.date === dateStr
        );
        
        if (dailyPrefs) {
          console.log('[DigestScreen] Found daily preferences for today:', dailyPrefs);
        } else {
          console.log('[DigestScreen] No daily preferences found for today');
        }
      } catch (contextError) {
        console.error('[DigestScreen] Error checking context:', contextError);
      }
      
      try {
        console.log('[DigestScreen] Calling createDigestOnAPI...');
        const result = await createDigestOnAPI(dateStr);
        console.log('[DigestScreen] Digest refresh successful:', result.activities?.length || 0, 'activities');
        
        // Log the first few activities to see what we got
        if (result.activities && result.activities.length > 0) {
          console.log('[DigestScreen] First activity:', result.activities[0]);
          console.log('[DigestScreen] Activity titles:', result.activities.map(a => a.title).slice(0, 5));
        }
        
        setDayDigest(result);
      } catch (error) {
        console.error('[DigestScreen] Error refreshing digest, retrying:', error);
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        console.log('[DigestScreen] Retrying with isRetry=true...');
        const result = await createDigestOnAPI(dateStr, true);
        console.log('[DigestScreen] Digest retry successful:', result.activities?.length || 0, 'activities');
        
        // Log the retry results
        if (result.activities && result.activities.length > 0) {
          console.log('[DigestScreen] Retry first activity:', result.activities[0]);
          console.log('[DigestScreen] Retry activity titles:', result.activities.map(a => a.title).slice(0, 5));
        }
        
        setDayDigest(result);
      }
    } catch (error) {
      console.error('[DigestScreen] Failed to refresh digest after retry:', error);
      setRefreshError('Unable to generate digest. Please try again later.');
    } finally {
      setIsRefreshing(false);
      setIsLoading(false);
    }
  };

  const formatDateHeader = (date: Date) => {
    const today = new Date();
    const yesterday = subDays(today, 1);
    const tomorrow = addDays(today, 1);

    if (format(date, 'yyyy-MM-dd') === format(today, 'yyyy-MM-dd')) {
      return 'Today';
    } else if (format(date, 'yyyy-MM-dd') === format(yesterday, 'yyyy-MM-dd')) {
      return 'Yesterday';
    } else if (format(date, 'yyyy-MM-dd') === format(tomorrow, 'yyyy-MM-dd')) {
      return 'Tomorrow';
    }

    return format(date, 'MMMM d');
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>Loading daily digest...</Text>
        </View>
      );
    }

    if (refreshError) {
      return (
        <View style={styles.errorContainer}>
          <Text style={[styles.errorTitle, { color: colors.text }]}>Error loading digest</Text>
          <Text style={[styles.errorText, { color: colors.textSecondary }]}>{refreshError}</Text>
          <TouchableOpacity 
            style={styles.retryButton} 
            onPress={() => refreshDigest()}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      );
    }

    if (!dayDigest || !dayDigest.activities || dayDigest.activities.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="calendar-outline" size={64} color={colors.text} style={styles.emptyIcon} />
          <Text style={[styles.emptyTitle, { color: colors.text }]}>No activities planned yet</Text>
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
            Generate your personalized daily plan including meals, workouts, hydration reminders, and wellness activities.
          </Text>
          <TouchableOpacity 
            style={styles.generateButton} 
            onPress={() => refreshDigest()}
            disabled={isRefreshing}
          >
            <Text style={styles.generateButtonText}>
              {isRefreshing ? 'Creating Your Plan...' : 'Create Daily Plan'}
            </Text>
            {isRefreshing && (
              <ActivityIndicator 
                size="small" 
                color="#FFFFFF"
                style={{ marginLeft: 8 }}
              />
            )}
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View style={styles.contentContainer}>
        <FlatList
          data={dayDigest.activities}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <ActivityItem 
              activity={item} 
              onToggleComplete={handleToggleComplete}
              onViewDetails={handleViewDetails}
            />
          )}
          contentContainerStyle={dayDigest.activities.length === 0 ? { flex: 1, justifyContent: 'center', alignItems: 'center' } : { paddingBottom: 20 }}
          ListEmptyComponent={
            <View style={styles.emptyListContainer}>
              <Text style={[styles.emptyListText, { color: colors.text }]}>No activities planned for today.</Text>
              <TouchableOpacity 
                style={styles.generateButton} 
                onPress={() => refreshDigest()}
              >
                <Text style={styles.generateButtonText}>Generate Suggestions</Text>
              </TouchableOpacity>
            </View>
          }
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={refreshDigest}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }
        />
      </View>
    );
  };

  // Format time to user's local timezone
  const formatLocalTime = (timeString: string) => {
    if (!timeString) return '';
    
    try {
      // Check if it's an ISO string (contains 'T')
      if (timeString.includes('T')) {
        // Parse as ISO date string
        const scheduledDate = new Date(timeString);
        
        // Check if the date is valid
        if (isNaN(scheduledDate.getTime())) {
          return timeString; // Fallback if invalid
        }
        
        // Format in user's local timezone
        return scheduledDate.toLocaleTimeString([], { 
          hour: 'numeric', 
          minute: '2-digit',
          hour12: true 
        });
      } else {
        // Handle legacy format "HH:MM"
        const today = new Date();
        const [hours, minutes] = timeString.split(':').map(Number);
        
        // Check if hours and minutes are valid
        if (isNaN(hours) || isNaN(minutes)) {
          return timeString; // Fallback if invalid
        }
        
        const scheduledDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), hours, minutes);
        
        // Format in user's local timezone
        return scheduledDate.toLocaleTimeString([], { 
          hour: 'numeric', 
          minute: '2-digit',
          hour12: true 
        });
      }
    } catch (error) {
      console.warn('Error formatting time:', error);
      return timeString; // Fallback to original string if parsing fails
    }
  };

  // Get next upcoming activity
  const getNextActivity = () => {
    if (!dayDigest?.activities) return null;
    
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes(); // Current time in minutes
    
    // Find the next uncompleted activity
    const upcomingActivities = dayDigest.activities
      .filter((activity: any) => !activity.completed)
      .map((activity: any) => {
        // Parse scheduled time if available
        let activityTime = 0;
        if (activity.scheduledTime) {
          try {
            if (activity.scheduledTime.includes('T')) {
              // Handle ISO date string
              const scheduledDate = new Date(activity.scheduledTime);
              if (!isNaN(scheduledDate.getTime())) {
                activityTime = scheduledDate.getHours() * 60 + scheduledDate.getMinutes();
              }
            } else {
              // Handle legacy "HH:MM" format
              const [hours, minutes] = activity.scheduledTime.split(':').map(Number);
              if (!isNaN(hours) && !isNaN(minutes)) {
                activityTime = hours * 60 + minutes;
              }
            }
          } catch (error) {
            console.warn('Error parsing scheduled time:', error);
          }
        }
        return { ...activity, timeInMinutes: activityTime };
      })
      .sort((a: any, b: any) => a.timeInMinutes - b.timeInMinutes);
    
    // Find the next activity after current time, or the first activity if none are after current time
    let nextActivity = upcomingActivities.find((activity: any) => activity.timeInMinutes > currentTime);
    if (!nextActivity && upcomingActivities.length > 0) {
      nextActivity = upcomingActivities[0]; // First activity of the day if we're past all scheduled times
    }
    
    return nextActivity;
  };

  // Get activity icon based on type
  const getActivityIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'meal':
        return 'restaurant';
      case 'workout':
        return 'fitness';
      case 'hydration':
        return 'water';
      case 'wellness':
        return 'leaf';
      case 'supplement':
        return 'medical';
      default:
        return 'checkmark-circle';
    }
  };

  // Render main action buttons
  const renderMainActions = () => {
    const nextActivity = getNextActivity();
    
    if (nextActivity) {
      return (
        <View style={styles.mainActionsContainer}>
          <TouchableOpacity
            style={[styles.primaryActionButton, { backgroundColor: colors.primary }]}
            onPress={() => handleSelectActivity(nextActivity)}
            activeOpacity={0.8}
          >
            <View style={styles.actionButtonContent}>
              <View style={styles.nextActivityBadge}>
                <Ionicons name="arrow-forward-circle" size={16} color="#fff" />
                <Text style={styles.nextActivityBadgeText}>NEXT UP</Text>
              </View>
              <Ionicons name={getActivityIcon(nextActivity.type) as any} size={32} color="#fff" />
              <Text style={styles.primaryActionText}>{nextActivity.title}</Text>
              <Text style={styles.primaryActionSubtext}>
                {nextActivity.scheduledTime ? `Scheduled for ${formatLocalTime(nextActivity.scheduledTime)}` : 'Next activity for today'}
              </Text>
            </View>
          </TouchableOpacity>

          <View style={styles.secondaryActionsRow}>
            <TouchableOpacity
              style={[styles.secondaryActionButton, { backgroundColor: colors.card, borderColor: colors.border }]}
              onPress={() => handleToggleComplete(nextActivity.id)}
              activeOpacity={0.7}
            >
              <Ionicons name="checkmark-circle-outline" size={24} color={colors.primary} />
              <Text style={[styles.secondaryActionText, { color: colors.text }]}>Mark Done</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.secondaryActionButton, { backgroundColor: colors.card, borderColor: colors.border }]}
              onPress={refreshDigest}
              activeOpacity={0.7}
              disabled={isRefreshing}
            >
              <Ionicons name="refresh" size={24} color={colors.primary} />
              <Text style={[styles.secondaryActionText, { color: colors.text }]}>Refresh</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.secondaryActionButton, { backgroundColor: colors.card, borderColor: colors.border }]}
              onPress={handleAddContext}
              activeOpacity={0.7}
            >
              <Ionicons name="add-circle-outline" size={24} color={colors.primary} />
              <Text style={[styles.secondaryActionText, { color: colors.text }]}>Context</Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    // Fallback to generate digest if no activities
    return (
      <View style={styles.mainActionsContainer}>
        <TouchableOpacity
          style={[styles.primaryActionButton, { backgroundColor: colors.primary }]}
          onPress={refreshDigest}
          activeOpacity={0.8}
          disabled={isRefreshing}
        >
          <View style={styles.actionButtonContent}>
            <Ionicons name="sparkles" size={32} color="#fff" />
            <Text style={styles.primaryActionText}>Generate Digest</Text>
            <Text style={styles.primaryActionSubtext}>Create your personalized daily plan</Text>
          </View>
        </TouchableOpacity>

        <View style={styles.secondaryActionsRow}>
          <TouchableOpacity
            style={[styles.secondaryActionButton, { backgroundColor: colors.card, borderColor: colors.border }]}
            onPress={handleAddContext}
            activeOpacity={0.7}
          >
            <Ionicons name="add-circle-outline" size={24} color={colors.primary} />
            <Text style={[styles.secondaryActionText, { color: colors.text }]}>Context</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.secondaryActionButton, { backgroundColor: colors.card, borderColor: colors.border }]}
            onPress={() => setActiveTab('calendar')}
            activeOpacity={0.7}
          >
            <Ionicons name="calendar-outline" size={24} color={colors.primary} />
            <Text style={[styles.secondaryActionText, { color: colors.text }]}>Calendar</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.secondaryActionButton, { backgroundColor: colors.card, borderColor: colors.border }]}
            onPress={() => setActiveTab('insights')}
            activeOpacity={0.7}
          >
            <Ionicons name="analytics-outline" size={24} color={colors.primary} />
            <Text style={[styles.secondaryActionText, { color: colors.text }]}>Insights</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  // Render quick stats
  const renderQuickStats = () => {
    const activities = dayDigest?.activities || [];
    const completedActivities = activities.filter((a: any) => a.completed).length;
    const totalActivities = activities.length;
    const mealsCount = activities.filter((a: any) => a.type === 'meal').length;
    const workoutsCount = activities.filter((a: any) => a.type === 'workout').length;
    const completedMeals = activities.filter((a: any) => a.type === 'meal' && a.completed).length;
    const completedWorkouts = activities.filter((a: any) => a.type === 'workout' && a.completed).length;

    // Calculate completion percentage
    const completionPercentage = totalActivities > 0 ? Math.round((completedActivities / totalActivities) * 100) : 0;

    return (
      <View style={[styles.quickStatsContainer, { backgroundColor: colors.card }]}>
        <Text style={[styles.quickStatsTitle, { color: colors.text }]}>Today's Progress</Text>
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.primary }]}>{completionPercentage}%</Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Complete</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.primary }]}>{completedMeals}/{mealsCount}</Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Meals</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.primary }]}>{completedWorkouts}/{workoutsCount}</Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Workouts</Text>
          </View>
        </View>
      </View>
    );
  };

  // Render content based on active tab
  const renderTabContent = () => {
    switch (activeTab) {
      case 'calendar':
        return (
          <View style={styles.tabContent}>
            <View style={styles.sectionHeaderSimple}>
              <Text style={[styles.sectionTitleSimple, { color: colors.text }]}>Date Navigation</Text>
            </View>
            
            <View style={styles.dateNavigation}>
              <TouchableOpacity
                style={[styles.navButton, { backgroundColor: colors.card }]}
                onPress={handlePreviousDay}
                disabled={isTransitioning || isRefreshing}
              >
                <Ionicons name="chevron-back" size={20} color={colors.primary} />
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.dateContainer}
                onPress={handleOpenCalendar}
              >
                <Text style={[styles.dateText, { color: colors.text }]}>
                  {formatDateHeader(selectedDate)}
                </Text>
                <Text style={[styles.dateSubtext, { color: colors.textSecondary }]}>
                  {format(selectedDate, 'EEEE, yyyy')}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.navButton, { backgroundColor: colors.card }]}
                onPress={handleNextDay}
                disabled={isTransitioning || isRefreshing}
              >
                <Ionicons name="chevron-forward" size={20} color={colors.primary} />
              </TouchableOpacity>
            </View>

            <View style={styles.timelineContainer}>
              {renderContent()}
            </View>
          </View>
        );

      case 'insights':
        return (
          <View style={styles.tabContent}>
            <View style={styles.sectionHeaderSimple}>
              <Text style={[styles.sectionTitleSimple, { color: colors.text }]}>Digest Insights</Text>
            </View>
            
            <View style={[styles.insightsContainer, { backgroundColor: colors.card }]}>
              <Text style={[styles.insightsText, { color: colors.textSecondary }]}>
                Insights and analytics coming soon! This will show your digest patterns, completion rates, and personalized recommendations.
              </Text>
            </View>
          </View>
        );

      default: // 'today'
        return (
          <View style={styles.tabContent}>
            {renderQuickStats()}
            
            <View style={styles.sectionHeaderSimple}>
              <Text style={[styles.sectionTitleSimple, { color: colors.text }]}>
                {formatDateHeader(selectedDate)}
              </Text>
              <TouchableOpacity
                style={[styles.refreshButton, { backgroundColor: colors.card }]}
                onPress={refreshDigest}
                disabled={isRefreshing}
              >
                <Ionicons 
                  name="refresh" 
                  size={18} 
                  color={colors.primary} 
                  style={[
                    styles.refreshIcon,
                    isRefreshing && styles.refreshingIcon
                  ]} 
                />
              </TouchableOpacity>
            </View>

            <View style={styles.timelineContainer}>
              {renderContent()}
            </View>
          </View>
        );
    }
  };

  // Test function for debugging digest generation
  const testDigestGeneration = async () => {
    console.log('===== DIGEST GENERATION DEBUG TEST =====');
    const dateStr = format(selectedDate, 'yyyy-MM-dd');
    
    try {
      const { testDigestGeneration } = require('../services/digestService');
      const results = await testDigestGeneration(dateStr);
      
      console.log('Test Results:');
      console.log('- API Digest:', results.apiDigest ? `${results.apiDigest.activities.length} activities` : 'Failed');
      console.log('- Local Digest:', results.localDigest ? `${results.localDigest.activities.length} activities` : 'Failed');
      console.log('- Fallback Digest:', results.fallbackDigest ? `${results.fallbackDigest.activities.length} activities` : 'Failed');
      console.log('- Errors:', results.errors.length);
      
      if (results.errors.length > 0) {
        console.log('Error details:', results.errors);
      }
      
      // Show which digest has the most realistic data
      if (results.apiDigest && results.apiDigest.activities.length > 0) {
        console.log('API Digest activities:', results.apiDigest.activities.map((a: any) => a.title));
      }
      if (results.localDigest && results.localDigest.activities.length > 0) {
        console.log('Local Digest activities:', results.localDigest.activities.map((a: any) => a.title));
      }
      
    } catch (error) {
      console.error('Test failed:', error);
    }
    
    console.log('=========================================');
  };

  // Test function for debugging context system
  const testContextSystem = async () => {
    console.log('===== CONTEXT SYSTEM DEBUG TEST =====');
    const dateStr = format(selectedDate, 'yyyy-MM-dd');
    
    try {
      const { contextEngineV3, ContextType } = require('../services/contextEngineV3');

      // Test saving context using V3 engine
      console.log('1. Testing context save with V3 engine...');
      const saveResult = await contextEngineV3.updateContext(
        ContextType.PREFERENCES,
        'I wake up at 10:00 AM and prefer protein-rich meals',
        {
          source: 'test',
          category: 'daily_preferences',
          date: dateStr,
          wakeUpTime: '10:00'
        }
      );
      console.log('Save result:', saveResult);

      // Test retrieving context
      console.log('2. Testing context retrieval...');
      const contextEntries = await contextEngineV3.getContextEntries({
        types: [ContextType.PREFERENCES, ContextType.CUSTOM],
        activeOnly: true
      });

      console.log('Total context entries:', contextEntries.length);

      // Look for our test context
      const testEntry = contextEntries.find((item: any) =>
        item.category === 'daily_preferences' &&
        item.metadata?.date === dateStr
      );
      
      if (testEntry) {
        console.log('✅ Found test context entry:', testEntry);
      } else {
        console.log('❌ Test context entry not found');
        console.log('Recent context entries:', contextEntries.slice(0, 3));
      }
      
      // Test API call with context
      console.log('3. Testing API call with context...');
      const { createDigestOnAPI } = require('../services/digestService');
      const result = await createDigestOnAPI(dateStr);
      
      console.log('API result activities:', result.activities?.length || 0);
      if (result.activities && result.activities.length > 0) {
        console.log('First activity:', result.activities[0].title);
        
        // Check if wake-up time was respected
        const breakfastActivity = result.activities.find((a: any) => 
          a.title.toLowerCase().includes('breakfast')
        );
        
        if (breakfastActivity) {
          console.log('Breakfast activity time:', breakfastActivity.scheduledTime);
          const scheduledTime = new Date(breakfastActivity.scheduledTime);
          const hour = scheduledTime.getHours();
          
          if (hour >= 10) {
            console.log('✅ Wake-up time appears to be respected (breakfast at', hour + ':00)');
          } else {
            console.log('❌ Wake-up time not respected (breakfast at', hour + ':00, expected after 10:00)');
          }
        }
      }
      
    } catch (error) {
      console.error('Context test failed:', error);
    }
    
    console.log('=========================================');
  };

  // Make the test functions available globally
  React.useEffect(() => {
    (global as any).testDigestGeneration = testDigestGeneration;
    (global as any).testContextSystem = testContextSystem;
    (global as any).debugTokens = debugTokens;
    (global as any).testApiEndpoints = testApiEndpoints;
    (global as any).testDigestRegenerate = testDigestRegenerate;
    return () => {
      delete (global as any).testDigestGeneration;
      delete (global as any).testContextSystem;
      delete (global as any).debugTokens;
      delete (global as any).testApiEndpoints;
      delete (global as any).testDigestRegenerate;
    };
  }, [selectedDate]);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <Header
        title="Daily Digest"
      />
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={refreshDigest} colors={[colors.primary]} />
        }
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Main Actions - Always visible */}
        {renderMainActions()}

        {/* Tab Navigation */}
        <View style={[styles.tabNavigation, { backgroundColor: colors.card }]}>
          {[
            { key: 'today', label: 'Today', icon: 'today-outline' },
            { key: 'calendar', label: 'Calendar', icon: 'calendar-outline' },
            { key: 'insights', label: 'Insights', icon: 'analytics-outline' },
          ].map((tab) => (
            <TouchableOpacity
              key={tab.key}
              style={[
                styles.tabButton,
                activeTab === tab.key && { backgroundColor: colors.primary + '20' }
              ]}
              onPress={() => setActiveTab(tab.key as any)}
              activeOpacity={0.7}
            >
              <Ionicons
                name={tab.icon as any}
                size={20}
                color={activeTab === tab.key ? colors.primary : colors.textSecondary}
              />
              <Text
                style={[
                  styles.tabLabel,
                  { color: activeTab === tab.key ? colors.primary : colors.textSecondary }
                ]}
              >
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Tab Content */}
        {renderTabContent()}
      </ScrollView>

      {isRefreshing && (
        <Animated.View
          entering={FadeIn.duration(200)}
          exiting={FadeOut.duration(200)}
          style={[styles.loadingOverlay, { backgroundColor: colors.background + 'CC' }]}
        >
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={[styles.loadingText, { color: colors.text }]}>
              Creating your comprehensive daily plan...
            </Text>
            <Text style={[styles.loadingSubtext, { color: colors.textSecondary }]}>
              Generating personalized meals, workouts, and wellness activities
            </Text>
          </View>
        </Animated.View>
      )}

      {refreshError && !isRefreshing && (
        <Animated.View
          entering={FadeIn.duration(300)}
          exiting={FadeOut.duration(300)}
          style={[styles.errorContainer, { backgroundColor: colors.error + '10' }]}
        >
          <Text style={[styles.errorText, { color: colors.error }]}>
            {refreshError}
          </Text>
        </Animated.View>
      )}

      <DigestCalendar
        visible={showCalendar}
        onClose={() => setShowCalendar(false)}
        selectedDate={selectedDate}
        onSelectDate={handleSelectDate}
      />

      <ActivityEditModal
        visible={showEditModal}
        onClose={() => setShowEditModal(false)}
        activity={selectedActivity}
        onSave={handleSaveActivity}
      />

      <DigestContextModal
        visible={showContextModal}
        onClose={() => setShowContextModal(false)}
        onSuccess={handleContextSuccess}
        date={selectedDate}
        resetFuturePlans={false}
      />

      <DigestContextModal
        visible={showResetModal}
        onClose={() => setShowResetModal(false)}
        onSuccess={handleContextSuccess}
        date={selectedDate}
        resetFuturePlans={true}
      />

      <MealDetailModal
        visible={showMealModal}
        onClose={() => setShowMealModal(false)}
        activity={selectedActivity}
      />

      <WorkoutDetailModal
        visible={showWorkoutModal}
        onClose={() => setShowWorkoutModal(false)}
        activity={selectedActivity}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: Platform.OS === 'android' ? 12 : 8,
    height: 60,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    fontFamily: typography.fontFamily.semibold,
  },
  headerButton: {
    padding: 6,
    marginLeft: 8,
  },
  dateNavigation: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  navButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },
  dateContainer: {
    alignItems: 'center',
  },
  dateText: {
    fontFamily: typography.fontFamily.semibold,
    fontSize: typography.sizes.lg,
  },
  dateSubtext: {
    fontFamily: typography.fontFamily.regular,
    fontSize: typography.sizes.sm,
    marginTop: 2,
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.semibold,
  },
  scrollContent: {
    paddingHorizontal: 15,
    paddingBottom: Platform.OS === 'ios' ? 90 : 80,
  },
  // New styles for redesigned layout
  mainActionsContainer: {
    paddingVertical: 20,
    paddingHorizontal: 5,
  },
  primaryActionButton: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  },
  actionButtonContent: {
    alignItems: 'center',
  },
  nextActivityBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginBottom: 12,
  },
  nextActivityBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontFamily: typography.fontFamily.bold,
    marginLeft: 6,
    letterSpacing: 0.5,
  },
  primaryActionText: {
    color: '#fff',
    fontSize: 20,
    fontFamily: typography.fontFamily.bold,
    marginTop: 8,
  },
  primaryActionSubtext: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    fontFamily: typography.fontFamily.regular,
    marginTop: 4,
  },
  secondaryActionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  secondaryActionButton: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    marginHorizontal: 4,
    borderRadius: 12,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  secondaryActionText: {
    fontSize: 14,
    fontFamily: typography.fontFamily.medium,
    marginTop: 6,
  },
  tabNavigation: {
    flexDirection: 'row',
    marginHorizontal: 5,
    marginBottom: 20,
    borderRadius: 12,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
  },
  tabLabel: {
    fontSize: 14,
    fontFamily: typography.fontFamily.medium,
    marginLeft: 6,
  },
  tabContent: {
    flex: 1,
  },
  quickStatsContainer: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    marginHorizontal: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  quickStatsTitle: {
    fontSize: 16,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 12,
  },
  statsRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontFamily: typography.fontFamily.bold,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: typography.fontFamily.regular,
    marginTop: 4,
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: 'rgba(150, 150, 150, 0.3)',
    marginHorizontal: 16,
  },
  sectionHeaderSimple: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 5,
  },
  sectionTitleSimple: {
    fontSize: 18,
    fontFamily: typography.fontFamily.semibold,
  },
  insightsContainer: {
    borderRadius: 12,
    padding: 20,
    marginHorizontal: 5,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 200,
  },
  insightsText: {
    fontSize: 16,
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
    lineHeight: 24,
  },
  timelineContainer: {
    flex: 1,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  loadingContainer: {
    padding: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    maxWidth: '80%',
  },
  loadingText: {
    fontFamily: typography.fontFamily.semibold,
    fontSize: typography.sizes.md,
    marginTop: 16,
    textAlign: 'center',
  },
  loadingSubtext: {
    fontFamily: typography.fontFamily.regular,
    fontSize: typography.sizes.sm,
    marginTop: 8,
    textAlign: 'center',
  },
  errorContainer: {
    position: 'absolute',
    top: 120,
    left: 16,
    right: 16,
    padding: 12,
    borderRadius: 8,
    zIndex: 5,
  },
  errorText: {
    fontFamily: typography.fontFamily.medium,
    fontSize: typography.sizes.sm,
    textAlign: 'center',
  },
  errorTitle: {
    fontFamily: typography.fontFamily.semibold,
    fontSize: typography.sizes.md,
    marginBottom: 8,
  },
  retryButton: {
    padding: 12,
    borderWidth: 1,
    borderColor: '#7188f0',
    borderRadius: 8,
    alignItems: 'center',
  },
  retryButtonText: {
    fontFamily: typography.fontFamily.semibold,
    fontSize: typography.sizes.sm,
    color: '#7188f0',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyIcon: {
    marginBottom: 16,
  },
  emptyTitle: {
    fontFamily: typography.fontFamily.semibold,
    fontSize: typography.sizes.lg,
    marginBottom: 8,
  },
  emptyText: {
    fontFamily: typography.fontFamily.regular,
    fontSize: typography.sizes.sm,
    textAlign: 'center'
  },
  generateButton: {
    padding: 12,
    borderWidth: 1,
    borderColor: '#7188f0',
    borderRadius: 8,
    alignItems: 'center',
  },
  generateButtonText: {
    fontFamily: typography.fontFamily.semibold,
    fontSize: typography.sizes.sm,
    color: '#7188f0',
  },
  contentContainer: {
    flex: 1,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyListText: {
    fontFamily: typography.fontFamily.regular,
    fontSize: typography.sizes.md,
    textAlign: 'center',
  },
  refreshButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  refreshIcon: {
    opacity: 0.8,
  },
  refreshingIcon: {
    opacity: 0.5,
  },
});

export default DigestScreen;

import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  SafeAreaView,
  FlatList,
  Platform,
  Alert,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../theme/typography';
import { useTheme } from '../theme/ThemeProvider';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import type { MainTabParamList } from '../navigation/EnhancedTabNavigator';
import { getLogs, Log, WorkoutData, WorkoutExercise } from '../services/conversationService';
import ServiceStatusBanner from '../components/common/ServiceStatusBanner';
import Header from '../components/common/Header';
import TimeDisplay from '../components/TimeDisplay';
import MiniWeatherDisplay from '../components/MiniWeatherDisplay';
import { getCurrentWeather, WeatherData } from '../services/nutritionService';
import {
  checkLocationPermission,
  requestLocationPermission,
  getLocationPermissionStatus,
  areLocationServicesEnabled,
  PermissionStatus
} from '../services/locationService';
import LocationPermissionBanner from '../components/LocationPermissionBanner';
import WorkoutTemplateSelector from '../components/strength/WorkoutTemplateSelector';
import CreateWorkoutModal from '../components/strength/CreateWorkoutModal';
import ActiveWorkoutModal from '../components/strength/ActiveWorkoutModal';
import StartWorkoutModal from '../components/strength/StartWorkoutModal';
import SimpleExerciseSelector from '../components/strength/SimpleExerciseSelector';
import StrengthChart from '../components/strength/StrengthChart';
import StrengthProfile from '../components/strength/StrengthProfile';
import WorkoutHistory from '../components/strength/WorkoutHistory';
import WorkoutSelectionModal from '../components/strength/WorkoutSelectionModal';

const { width } = Dimensions.get('window');

// Exercise data structure
interface Exercise {
  id: string;
  name: string;
  muscleGroup?: string;
  lastWeight?: number;
  lastReps?: number;
  lastUsed?: string;
  favorite?: boolean;
  pr?: {
    weight: number;
    reps: number;
    date: string;
  };
}

export default function StrengthScreen() {
  const { colors, isDark } = useTheme();
  const navigation = useNavigation<BottomTabNavigationProp<MainTabParamList>>();
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [workouts, setWorkouts] = useState<Log[]>([]);
  const [exercises, setExercises] = useState<Exercise[]>([]);
  const [selectedExercise, setSelectedExercise] = useState<Exercise | null>(null);
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'year' | 'all'>('month');
  const [exerciseHistory, setExerciseHistory] = useState<any[]>([]);
  const [progressRate, setProgressRate] = useState<number>(0);
  const [suggestedWeight, setSuggestedWeight] = useState<number>(0);
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);
  const [loadingWeather, setLoadingWeather] = useState(true);
  const [locationPermission, setLocationPermission] = useState<PermissionStatus>('undetermined');
  const [showLocationBanner, setShowLocationBanner] = useState(false);
  const [areServicesEnabled, setAreServicesEnabled] = useState(true);

  // Service status state
  const [showServiceBanner, setShowServiceBanner] = useState(false);

  // Workout template state
  const [workoutTemplates, setWorkoutTemplates] = useState<WorkoutData[]>([]);
  const [loadingTemplates, setLoadingTemplates] = useState(true);

  // Workout history state
  const [workoutHistory, setWorkoutHistory] = useState<any[]>([]);

  // Modal state
  const [showCreateWorkoutModal, setShowCreateWorkoutModal] = useState(false);
  const [showActiveWorkoutModal, setShowActiveWorkoutModal] = useState(false);
  const [showStartWorkoutModal, setShowStartWorkoutModal] = useState(false);
  const [showWorkoutSelectionModal, setShowWorkoutSelectionModal] = useState(false);
  const [selectedWorkout, setSelectedWorkout] = useState<WorkoutData | null>(null);

  // New state for simplified view
  const [activeTab, setActiveTab] = useState<'workout' | 'progress' | 'history'>('workout');
  const [showAdvancedView, setShowAdvancedView] = useState(false);

  useEffect(() => {
    const initScreen = async () => {
      setIsLoading(true);
      try {
        // Import preload service
        const { preloadScreenData, isDataLoaded } = require('../services/preLoad');
        
        // First check location permissions (needed for weather)
        await checkLocationStatus();
        
        // Load all screen data in parallel
        const screenData = await preloadScreenData('StrengthScreen');
        
        // Set weather data if available
        if (screenData.weather) {
          setWeatherData(screenData.weather);
        } else {
          // If no weather data in preload, try to load it directly
          loadWeatherData(false);
        }
        setLoadingWeather(false);
        
        // Load remaining data
        await loadData();
      } catch (error) {
        console.error('Error initializing StrengthScreen:', error);
        setLoadingWeather(false);
      } finally {
        setIsLoading(false);
      }
    };
    
    initScreen();
  }, []);

  const checkLocationStatus = async () => {
    try {
      const permissionStatus = await getLocationPermissionStatus();
      setLocationPermission(permissionStatus);
      const servicesEnabled = await areLocationServicesEnabled();
      setAreServicesEnabled(servicesEnabled);
      setShowLocationBanner(permissionStatus !== 'granted' || !servicesEnabled);
    } catch (error) {
      console.error('Error checking location status in StrengthScreen:', error);
      setShowLocationBanner(true);
    }
  };

  const loadWeatherData = async (forceRefresh = false) => {
    setLoadingWeather(true);
    try {
      if (locationPermission !== 'granted' || showLocationBanner) {
        await checkLocationStatus();
      }
      const weather = await getCurrentWeather(forceRefresh);
      setWeatherData(weather);
    } catch (error) {
      console.error('Error loading weather data in StrengthScreen:', error);
      // Don't leave weatherData as null, set it to an unavailable state
      setWeatherData({ unavailable: true } as WeatherData);
    } finally {
      setLoadingWeather(false);
    }
  };

  useEffect(() => {
    if (selectedExercise) {
      updateExerciseHistory(selectedExercise);
    }
  }, [selectedExercise?.id]);

  const updateExerciseHistory = (exercise: Exercise) => {
    const history: any[] = [];
    workouts.forEach(log => {
      const workout = log.metrics?.workout as WorkoutData;
      if (workout && workout.exercises) {
        const exerciseData = workout.exercises.find(e => e.name === exercise.name);
        if (exerciseData) {
          let weight = 0;
          let reps = 0;

          if (Array.isArray(exerciseData.sets) && exerciseData.sets.length > 0) {
            const highestWeightSet = exerciseData.sets.reduce((prev, current) => {
              return (current.weight || 0) > (prev.weight || 0) ? current : prev;
            }, exerciseData.sets[0]);

            weight = highestWeightSet.weight || 0;
            reps = highestWeightSet.reps || 0;
          }

          if (weight > 0) {
            history.push({
              date: log.timestamp,
              weight,
              reps
            });
          }
        }
      }
    });

    history.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    const filteredHistory = filterHistoryByTimeRange(history, timeRange);
    setExerciseHistory(filteredHistory);

    if (filteredHistory.length >= 2) {
      const oldestWeight = filteredHistory[0].weight;
      const newestWeight = filteredHistory[filteredHistory.length - 1].weight;

      if (oldestWeight > 0) {
        const rate = Math.round(((newestWeight - oldestWeight) / oldestWeight) * 100);
        setProgressRate(rate);
      } else {
        setProgressRate(0);
      }

      if (newestWeight > 0) {
        const suggestedWeight = Math.round(newestWeight * 1.05);
        setSuggestedWeight(suggestedWeight);
      } else {
        setSuggestedWeight(0);
      }
    } else {
      setProgressRate(0);
      setSuggestedWeight(0);
    }
  };

  const loadData = async () => {
    setIsLoading(true);
    setLoadingTemplates(true);
    try {
      const logs = await getLogs();
      const workoutLogs = logs.filter(log => log.type === 'workout');
      setWorkouts(workoutLogs);

      const processWorkoutData = () => {
        const templates: WorkoutData[] = [];
        const templateMap = new Map<string, WorkoutData>();
        
        const completedWorkouts: any[] = [];
        
        const extractedExercises: Exercise[] = [];
        const exerciseMap = new Map<string, Exercise>();
        
        const exerciseHistoryMap = new Map<string, any[]>();
        
        workoutLogs.forEach(log => {
          const workoutData = log.metrics?.workout as WorkoutData;
          
          if (workoutData && !workoutData.completed) {
            if (!templateMap.has(workoutData.title)) {
              templateMap.set(workoutData.title, workoutData);
            }
          }
          else if (workoutData && workoutData.completed) {
            completedWorkouts.push({
              id: log.id,
              title: workoutData.title,
              date: log.timestamp,
              exercises: workoutData.exercises
            });
          }
          
          if (workoutData && workoutData.exercises) {
            workoutData.exercises.forEach(exercise => {
              const exerciseName = exercise.name;
              
              if (!exerciseHistoryMap.has(exerciseName)) {
                exerciseHistoryMap.set(exerciseName, []);
              }
              
              let weight = 0;
              let reps = 0;
              
              if (Array.isArray(exercise.sets) && exercise.sets.length > 0) {
                const highestWeightSet = exercise.sets.reduce((prev, current) => {
                  return (current.weight || 0) > (prev.weight || 0) ? current : prev;
                }, exercise.sets[0]);
                
                weight = highestWeightSet.weight || 0;
                reps = highestWeightSet.reps || 0;
              }
              
              if (weight > 0) {
                exerciseHistoryMap.get(exerciseName)?.push({
                  date: log.timestamp,
                  weight,
                  reps
                });
              }
              
              if (!exerciseMap.has(exerciseName)) {
                let muscleGroup = 'Other';
                const name = exerciseName.toLowerCase();
                
                if (name.includes('chest') || name.includes('bench') || name.includes('pec') || name.includes('fly')) {
                  muscleGroup = 'Chest';
                } else if (name.includes('back') || name.includes('row') || name.includes('pull') || name.includes('lat')) {
                  muscleGroup = 'Back';
                } else if (name.includes('shoulder') || name.includes('delt') || name.includes('press') || name.includes('raise')) {
                  muscleGroup = 'Shoulders';
                } else if (name.includes('arm') || name.includes('bicep') || name.includes('tricep') || name.includes('curl')) {
                  muscleGroup = 'Arms';
                } else if (name.includes('leg') || name.includes('quad') || name.includes('hamstring') || name.includes('squat') || name.includes('lunge')) {
                  muscleGroup = 'Legs';
                } else if (name.includes('core') || name.includes('ab') || name.includes('crunch') || name.includes('plank')) {
                  muscleGroup = 'Core';
                } else if (name.includes('full') || name.includes('body') || name.includes('burpee')) {
                  muscleGroup = 'Full Body';
                }
                
                const isFavorite = Array.from(workoutLogs).filter(l => 
                  (l.metrics?.workout as WorkoutData)?.exercises?.some(e => e.name === exerciseName)
                ).length > 3;
                
                exerciseMap.set(exerciseName, {
                  id: exerciseName.toLowerCase().replace(/\s+/g, '-'),
                  name: exerciseName,
                  muscleGroup,
                  lastWeight: weight,
                  lastReps: reps,
                  lastUsed: log.timestamp,
                  favorite: isFavorite,
                  pr: {
                    weight: weight,
                    reps: reps,
                    date: log.timestamp,
                  }
                });
              }
            });
          }
        });
        
        completedWorkouts.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
        
        setWorkoutHistory(completedWorkouts.slice(0, 5));
        
        setWorkoutTemplates(Array.from(templateMap.values()));
        
        const sortedExercises = Array.from(exerciseMap.values()).sort((a, b) => a.name.localeCompare(b.name));
        setExercises(sortedExercises);
        
        if (exerciseMap.size > 0 && !selectedExercise) {
          setSelectedExercise(Array.from(exerciseMap.values())[0]);
        } else if (selectedExercise) {
          updateExerciseHistory(selectedExercise);
        }
      };
      
      processWorkoutData();
      
    } catch (error) {
      console.error('Error loading strength data:', error);
    } finally {
      setIsLoading(false);
      setLoadingTemplates(false);
    }
  };

  const filterHistoryByTimeRange = (history: any[], range: 'week' | 'month' | 'year' | 'all') => {
    if (range === 'all') return history;

    const now = new Date();
    const cutoffDate = new Date();

    switch (range) {
      case 'week':
        cutoffDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        cutoffDate.setMonth(now.getMonth() - 1);
        break;
      case 'year':
        cutoffDate.setFullYear(now.getFullYear() - 1);
        break;
    }

    return history.filter(item => new Date(item.date) >= cutoffDate);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const handleExerciseSelect = (exercise: Exercise) => {
    setSelectedExercise(exercise);

    const logs = workouts.filter(log => {
      const workout = log.metrics?.workout as WorkoutData;
      return workout?.exercises?.some(e => e.name === exercise.name);
    });

    const history: any[] = [];
    logs.forEach(log => {
      const workout = log.metrics?.workout as WorkoutData;
      if (workout && workout.exercises) {
        const exerciseData = workout.exercises.find(e => e.name === exercise.name);
        if (exerciseData) {
          let weight = 0;
          let reps = 0;

          if (Array.isArray(exerciseData.sets) && exerciseData.sets.length > 0) {
            const highestWeightSet = exerciseData.sets.reduce((prev, current) => {
              return (current.weight || 0) > (prev.weight || 0) ? current : prev;
            }, exerciseData.sets[0]);

            weight = highestWeightSet.weight || 0;
            reps = highestWeightSet.reps || 0;
          }

          if (weight > 0) {
            history.push({
              date: log.timestamp,
              weight,
              reps
            });
          }
        }
      }
    });

    history.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    const filteredHistory = filterHistoryByTimeRange(history, timeRange);
    setExerciseHistory(filteredHistory);

    if (filteredHistory.length >= 2) {
      const oldestWeight = filteredHistory[0].weight;
      const newestWeight = filteredHistory[filteredHistory.length - 1].weight;

      if (oldestWeight > 0) {
        const rate = Math.round(((newestWeight - oldestWeight) / oldestWeight) * 100);
        setProgressRate(rate);
      } else {
        setProgressRate(0);
      }

      if (newestWeight > 0) {
        const suggestedWeight = Math.round(newestWeight * 1.05);
        setSuggestedWeight(suggestedWeight);
      } else {
        setSuggestedWeight(0);
      }
    } else {
      setProgressRate(0);
      setSuggestedWeight(0);
    }
  };

  const handleTimeRangeChange = (range: 'week' | 'month' | 'year' | 'all') => {
    setTimeRange(range);

    if (selectedExercise) {
      updateExerciseHistory(selectedExercise);
    }
  };

  const handleCreateWorkout = () => {
    setShowCreateWorkoutModal(true);
  };

  const handleOpenWorkoutSelection = () => {
    setShowWorkoutSelectionModal(true);
  };

  const handleCreateCustomWorkout = (prompt: string) => {
    const customWorkout: any = {
      title: "Custom Workout",
      exercises: [],
      customPrompt: prompt
    };

    setSelectedWorkout(customWorkout);
    setShowStartWorkoutModal(true);
  };

  const handleWorkoutCreated = (workout: WorkoutData) => {
    loadData();
  };

  const handleSelectWorkout = (workout: WorkoutData) => {
    console.log("Selected workout:", workout);
    setSelectedWorkout(workout);

    if ((workout as any).isCustom) {
      setShowActiveWorkoutModal(true);
    } else {
      setShowStartWorkoutModal(true);
    }
  };

  const handleStartWorkout = () => {
    setShowStartWorkoutModal(false);
    setShowActiveWorkoutModal(true);
  };

  const handleWorkoutCompleted = () => {
    loadData();
  };

  // Update the useFocusEffect hook to properly handle the weather timestamp
  useFocusEffect(
    React.useCallback(() => {
      const loadFreshData = async () => {
        if (!isLoading) {
          try {
            // Determine when to refresh weather data
            let shouldRefreshWeather = true;
            
            if (weatherData && typeof weatherData === 'object') {
              // Check if the object has a timestamp property
              const timestamp = weatherData.timestamp;
              
              if (timestamp) {
                // Convert timestamp to number if it's a string
                const lastUpdate = typeof timestamp === 'string' ? 
                  new Date(timestamp).getTime() : 
                  timestamp as number;
                  
                // Only refresh if more than 15 minutes old
                const fifteenMinutes = 15 * 60 * 1000;
                shouldRefreshWeather = (Date.now() - lastUpdate > fifteenMinutes);
              }
            }
            
            if (shouldRefreshWeather) {
              loadWeatherData(false);
            }
          } catch (error) {
            console.warn('Error refreshing data on focus:', error);
          }
        }
      };
      
      loadFreshData();
      
      return () => {
        // Clean up any subscriptions or timers if needed
      };
    }, [isLoading, weatherData])
  );

  if (isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  // Render main action buttons
  const renderMainActions = () => (
    <View style={styles.mainActionsContainer}>
      <TouchableOpacity
        style={[styles.primaryActionButton, { backgroundColor: colors.primary }]}
        onPress={handleOpenWorkoutSelection}
        activeOpacity={0.8}
      >
        <View style={styles.actionButtonContent}>
          <Ionicons name="play-circle" size={32} color="#fff" />
          <Text style={styles.primaryActionText}>Start Workout</Text>
          <Text style={styles.primaryActionSubtext}>Begin your training session</Text>
        </View>
      </TouchableOpacity>

      <View style={styles.secondaryActionsRow}>
        <TouchableOpacity
          style={[styles.secondaryActionButton, { backgroundColor: colors.card, borderColor: colors.border }]}
          onPress={handleCreateWorkout}
          activeOpacity={0.7}
        >
          <Ionicons name="add-circle-outline" size={24} color={colors.primary} />
          <Text style={[styles.secondaryActionText, { color: colors.text }]}>Create Workout</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  // Render quick stats
  const renderQuickStats = () => {
    const thisWeekWorkouts = workoutHistory.filter(w => {
      const workoutDate = new Date(w.date);
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return workoutDate >= weekAgo;
    }).length;

    const totalWorkouts = workoutHistory.length;
    const favoriteExercise = exercises.find(e => e.favorite) || exercises[0];

    return (
      <View style={[styles.quickStatsContainer, { backgroundColor: colors.card }]}>
        <Text style={[styles.quickStatsTitle, { color: colors.text }]}>This Week</Text>
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.primary }]}>{thisWeekWorkouts}</Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Workouts</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.primary }]}>{totalWorkouts}</Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Total</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.primary }]}>{exercises.length}</Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Exercises</Text>
          </View>
        </View>
      </View>
    );
  };

  // Render recent templates
  const renderRecentTemplates = () => (
    <View style={styles.templatesSection}>
      <View style={styles.sectionHeaderSimple}>
        <Text style={[styles.sectionTitleSimple, { color: colors.text }]}>Quick Start</Text>
        <TouchableOpacity
          style={[
            styles.advancedToggleButton,
            {
              backgroundColor: showAdvancedView ? colors.primary : colors.card,
              borderColor: colors.primary
            }
          ]}
          onPress={() => setShowAdvancedView(!showAdvancedView)}
          activeOpacity={0.7}
        >
          <Ionicons
            name={showAdvancedView ? "grid" : "list"}
            size={16}
            color={showAdvancedView ? colors.background : colors.primary}
          />
          <Text style={[
            styles.advancedToggleText,
            { color: showAdvancedView ? colors.background : colors.primary }
          ]}>
            {showAdvancedView ? 'Simple' : 'Advanced'}
          </Text>
        </TouchableOpacity>
      </View>

      {showAdvancedView ? (
        <WorkoutTemplateSelector
          workouts={workoutTemplates}
          isLoading={loadingTemplates}
          onSelectWorkout={handleSelectWorkout}
          onCreateNewWorkout={handleCreateWorkout}
        />
      ) : (
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.templatesScroll}>
          {workoutTemplates.slice(0, 5).map((template, index) => (
            <TouchableOpacity
              key={template.title || index}
              style={[styles.templateCard, { backgroundColor: colors.card, borderColor: colors.border }]}
              onPress={() => handleSelectWorkout(template)}
              activeOpacity={0.7}
            >
              <Ionicons name="barbell-outline" size={20} color={colors.primary} />
              <Text style={[styles.templateTitle, { color: colors.text }]} numberOfLines={2}>
                {template.title}
              </Text>
              <Text style={[styles.templateSubtitle, { color: colors.textSecondary }]}>
                {template.exercises.length} exercises
              </Text>
            </TouchableOpacity>
          ))}
          <TouchableOpacity
            style={[styles.templateCard, styles.addTemplateCard, { backgroundColor: colors.primaryLight + '20', borderColor: colors.primary }]}
            onPress={handleCreateWorkout}
            activeOpacity={0.7}
          >
            <Ionicons name="add" size={24} color={colors.primary} />
            <Text style={[styles.templateTitle, { color: colors.primary }]}>Create New</Text>
          </TouchableOpacity>
        </ScrollView>
      )}
    </View>
  );

  // Render content based on active tab
  const renderTabContent = () => {
    switch (activeTab) {
      case 'progress':
        return (
          <View style={styles.tabContent}>
            <View style={styles.sectionHeaderSimple}>
              <Text style={[styles.sectionTitleSimple, { color: colors.text }]}>Exercise Progress</Text>
            </View>
            
            <View style={{ height: 300, marginBottom: 20 }}>
              <SimpleExerciseSelector
                exercises={exercises}
                selectedExercise={selectedExercise}
                onSelectExercise={handleExerciseSelect}
              />
            </View>

            {selectedExercise && (
              <>
                <StrengthProfile
                  exercise={selectedExercise}
                  progressRate={progressRate}
                  suggestedWeight={suggestedWeight}
                />

                <View style={[styles.chartContainer, { backgroundColor: colors.card }]}>
                  <Text style={[styles.chartTitle, { color: colors.text }]}>
                    {selectedExercise.name} Progress
                  </Text>
                  <StrengthChart
                    data={exerciseHistory}
                    timeRange={timeRange}
                  />
                </View>

                <View style={styles.timeRangeContainer}>
                  {['week', 'month', 'year', 'all'].map((range) => (
                    <TouchableOpacity
                      key={range}
                      style={[
                        styles.timeRangeButton,
                        {
                          backgroundColor: timeRange === range ? colors.primary : colors.card,
                          borderColor: timeRange === range ? colors.primary : colors.border,
                          borderWidth: 1,
                        }
                      ]}
                      onPress={() => handleTimeRangeChange(range as any)}
                    >
                      <Text
                        style={[
                          styles.timeRangeText,
                          { color: timeRange === range ? '#fff' : colors.text }
                        ]}
                      >
                        {range.charAt(0).toUpperCase() + range.slice(1)}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </>
            )}
          </View>
        );

      case 'history':
        return (
          <View style={styles.tabContent}>
            <View style={styles.sectionHeaderSimple}>
              <Text style={[styles.sectionTitleSimple, { color: colors.text }]}>Workout History</Text>
            </View>
            <WorkoutHistory
              workouts={workoutHistory}
              onSelectWorkout={(workout) => {
                console.log('Selected workout history:', workout);
              }}
            />
          </View>
        );

      default: // 'workout'
        return (
          <View style={styles.tabContent}>
            {renderQuickStats()}
            {renderRecentTemplates()}
          </View>
        );
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Enhanced Header - consistent with other screens */}
      <Header
        title="Strength"
        weatherData={null}
        loadingWeather={false}
        onRefreshWeather={() => {}}
      />

      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={[colors.primary]} />
        }
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {showLocationBanner && (
          <LocationPermissionBanner
            permissionStatus={locationPermission}
            onRequestPermission={async () => {
              await requestLocationPermission();
              await checkLocationStatus();
              loadWeatherData();
            }}
            onOpenSettings={() => {
              Alert.alert("Open Settings", "Please enable location services in your device settings for accurate weather.");
            }}
            locationServicesEnabled={areServicesEnabled}
          />
        )}

        {/* Main Actions - Always visible */}
        {renderMainActions()}

        {/* Tab Navigation */}
        <View style={[styles.tabNavigation, { backgroundColor: colors.card }]}>
          {[
            { key: 'workout', label: 'Workout', icon: 'fitness-outline' },
            { key: 'progress', label: 'Progress', icon: 'trending-up-outline' },
            { key: 'history', label: 'History', icon: 'time-outline' },
          ].map((tab) => (
            <TouchableOpacity
              key={tab.key}
              style={[
                styles.tabButton,
                activeTab === tab.key && { backgroundColor: colors.primary + '20' }
              ]}
              onPress={() => setActiveTab(tab.key as any)}
              activeOpacity={0.7}
            >
              <Ionicons
                name={tab.icon as any}
                size={20}
                color={activeTab === tab.key ? colors.primary : colors.textSecondary}
              />
              <Text
                style={[
                  styles.tabLabel,
                  { color: activeTab === tab.key ? colors.primary : colors.textSecondary }
                ]}
              >
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Tab Content */}
        {renderTabContent()}
      </ScrollView>

      {/* Modals */}
      <CreateWorkoutModal
        visible={showCreateWorkoutModal}
        onClose={() => setShowCreateWorkoutModal(false)}
        onWorkoutCreated={handleWorkoutCreated}
      />

      <StartWorkoutModal
        visible={showStartWorkoutModal}
        workout={selectedWorkout}
        onClose={() => setShowStartWorkoutModal(false)}
        onStart={handleStartWorkout}
      />

      {selectedWorkout && (
        <ActiveWorkoutModal
          visible={showActiveWorkoutModal}
          onClose={() => setShowActiveWorkoutModal(false)}
          workout={selectedWorkout}
          onWorkoutCompleted={handleWorkoutCompleted}
        />
      )}

      <WorkoutSelectionModal
        visible={showWorkoutSelectionModal}
        onClose={() => setShowWorkoutSelectionModal(false)}
        workoutTemplates={workoutTemplates}
        onSelectWorkout={handleSelectWorkout}
        onCreateCustomWorkout={handleCreateCustomWorkout}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    paddingHorizontal: 16, // Consistent with DietScreen
    paddingVertical: Platform.OS === 'android' ? 12 : 8, // Consistent with DietScreen
    height: 60, // Consistent with DietScreen
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1, // Consistent with DietScreen
    // Removed marginBottom: 10
  },
  headerLeft: { // Added
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerRight: { // Added
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: { // Added (or ensure it exists and has marginLeft)
    padding: 6,
    marginLeft: 8,
  },
  title: { // New consistent title style
    fontSize: 20,
    fontWeight: 'bold',
    fontFamily: typography.fontFamily.semibold,
  },
  pageTitle: { // Renamed from headerTitle
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.semibold,
  },
  startWorkoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
    elevation: 2,
  },
  startWorkoutButtonText: {
    color: '#fff',
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.semibold,
  },
  scrollContent: {
    paddingHorizontal: 15,
    paddingBottom: Platform.OS === 'ios' ? 90 : 80, // Add extra padding for the tab bar
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
    paddingHorizontal: 5,
  },
  sectionIcon: {
    marginRight: 8,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
  },
  // Exercise selector styles moved to ExerciseSelector component
  timeRangeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
    paddingHorizontal: 5,
  },
  timeRangeButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    minWidth: 70,
    alignItems: 'center',
  },
  timeRangeText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
  },
  chartContainer: {
    borderRadius: 15,
    padding: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chartTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 15,
  },
  chartPlaceholder: {
    height: 200,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyState: {
    padding: 30,
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  emptyStateText: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginTop: 15,
    marginBottom: 5,
  },
  emptyStateSubtext: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
  },
  prContainer: {
    borderRadius: 15,
    padding: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  prHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  prTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginLeft: 8,
  },
  prContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  prItem: {
    width: '30%',
    marginBottom: 10,
  },
  prLabel: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 4,
  },
  prValue: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
  // New styles for redesigned layout
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.bold,
  },
  mainActionsContainer: {
    paddingVertical: 20,
    paddingHorizontal: 5,
  },
  primaryActionButton: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  },
  actionButtonContent: {
    alignItems: 'center',
  },
  primaryActionText: {
    color: '#fff',
    fontSize: 20,
    fontFamily: typography.fontFamily.bold,
    marginTop: 8,
  },
  primaryActionSubtext: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    fontFamily: typography.fontFamily.regular,
    marginTop: 4,
  },
  secondaryActionsRow: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  secondaryActionButton: {
    alignItems: 'center',
    padding: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  secondaryActionText: {
    fontSize: 14,
    fontFamily: typography.fontFamily.medium,
    marginTop: 6,
  },
  tabNavigation: {
    flexDirection: 'row',
    marginHorizontal: 5,
    marginBottom: 20,
    borderRadius: 12,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
  },
  tabLabel: {
    fontSize: 14,
    fontFamily: typography.fontFamily.medium,
    marginLeft: 6,
  },
  tabContent: {
    flex: 1,
  },
  quickStatsContainer: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    marginHorizontal: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  quickStatsTitle: {
    fontSize: 16,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 12,
  },
  statsRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontFamily: typography.fontFamily.bold,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: typography.fontFamily.regular,
    marginTop: 4,
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: 'rgba(150, 150, 150, 0.3)',
    marginHorizontal: 16,
  },
  templatesSection: {
    marginBottom: 20,
  },
  sectionHeaderSimple: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 5,
  },
  sectionTitleSimple: {
    fontSize: 18,
    fontFamily: typography.fontFamily.semibold,
  },
  viewAllText: {
    fontSize: 14,
    fontFamily: typography.fontFamily.medium,
  },
  advancedToggleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    borderWidth: 1,
    gap: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  advancedToggleText: {
    fontSize: 13,
    fontFamily: typography.fontFamily.semibold,
  },
  templatesScroll: {
    paddingHorizontal: 5,
  },
  templateCard: {
    width: 140,
    height: 100,
    borderRadius: 12,
    padding: 12,
    marginRight: 12,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  addTemplateCard: {
    borderStyle: 'dashed',
  },
  templateTitle: {
    fontSize: 14,
    fontFamily: typography.fontFamily.medium,
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 4,
  },
  templateSubtitle: {
    fontSize: 12,
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
  },
});

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  FlatList,
  Platform,
  ActivityIndicator,
  Alert,
  Keyboard,
  ScrollView,
  Animated,
  Modal,
  Vibration,
  // Don't import Easing from react-native, use reanimated one
} from 'react-native';
import { SafeAreaView } from 'react-native';
import ReAnimated, {
  FadeIn,
  FadeOut,
  SlideInDown
} from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../theme/typography';
import { useTheme } from '../theme/ThemeProvider';
import { useProfile } from '../context/ProfileContext';
import { useRoute, useNavigation, RouteProp } from '@react-navigation/native';
import { useConversations } from '../context/ConversationContext';
import { useAuth } from '../context/AuthContext';
import ChatLoadingIndicator from '../components/ui/ChatLoadingIndicator';
// Global loading removed to avoid animation conflicts
import { getStoredTokens } from '../services/auth';
import { sendMessageToChat, Message as ChatMessage, testChatAPIConnectivity } from '../services/chatService';
import type { MainTabParamList } from '../navigation/MainTabNavigator';
import RecommendedActivityWidget from '../components/conversation/RecommendedActivityWidget';
import { SkeletonRecommendedActivity } from '../components/common/SkeletonPlaceholder';

// Correct imports from conversationService
import {
  Log,
  LogType,
  WorkoutData,
  WorkoutExercise,
  MealData,
  Message,
  saveLog,
  getLogById,
  saveWorkoutFromConversation,
  saveMealFromConversation,
  syncWithCloud
} from '@/services/conversationService';

import { getApiUrl, setApiUrlOverride, checkApiUrl } from '../services/apiClient';
import { v4 as uuidv4 } from 'uuid';
import { getNextMeal, getNextWorkout, Recommendation, clearRecommendationCache } from '../services/recommendationService';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface ExtendedMessage extends ChatMessage { // Extend imported Message
  detectedWorkout?: WorkoutData;
  detectedMeal?: MealData;
  isLoading?: boolean;
}

// Local styles for ConversationItem
const itemStyles = StyleSheet.create({
  logItem: {
    flexDirection: 'column', // Changed to column to fix layout
    justifyContent: 'space-between',
    marginBottom: 8,
    height: 80, // Consistent height for all conversation items
  },
  logTypeIcon: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  logItemContent: {
    flex: 1,
    height: '100%', // Fill the container height
    justifyContent: 'center',
  },
  logText: {
    fontSize: 14,
    fontFamily: typography.fontFamily.medium
  },
  timestamp: {
    fontSize: 12,
    marginTop: 4,
    fontFamily: typography.fontFamily.regular
  },
});

// Helper function to decode JWT (basic implementation)
// In a real app, consider using a library like jwt-decode
const decodeJwt = (token: string): any | null => {
  try {
    const base64Url = token.split('.')[1];
    if (!base64Url) return null;
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    // Use Buffer if in Node.js env or atob in browser/RN env
    // Assuming atob is available in React Native
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(function (c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        })
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (e) {
    console.error("Failed to decode JWT:", e);
    return null;
  }
};

export default function LotusScreen() {
  const { colors, isDark } = useTheme();
  const { profile } = useProfile();
  const navigation = useNavigation();
  const route = useRoute<RouteProp<MainTabParamList, 'Lotus'>>();
  const { tokens } = useAuth();
  const {
    conversations,
    isLoading,
    error,
    fetchConversations,
    createConversation,
    addMessage,
    deleteConversation: deleteConversationFromContext,
    getConversationById,
    refreshConversationMessages
  } = useConversations();

  // State declarations using imported types
  const [input, setInput] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentConversation, setCurrentConversation] = useState<Log | null>(null); // Use Log
  const [recentLogs, setRecentLogs] = useState<Log[]>([]); // Use Log
  const [messages, setMessages] = useState<ExtendedMessage[]>([]); // Use ExtendedMessage
  const [contentHeight, setContentHeight] = useState(0);
  const [scrollViewHeight, setScrollViewHeight] = useState(0);
  const [isAtEnd, setIsAtEnd] = useState(true);
  const inputRef = useRef<TextInput>(null);
  const scrollViewRef = useRef<FlatList>(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const buttonScale = useRef(new Animated.Value(1)).current;
  const isInitialMount = useRef(true);
  const previousMessagesLength = useRef(0);
  const loadingUpdateTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const [detectedWorkout, setDetectedWorkout] = useState<WorkoutData | null>(null);
  const [showWorkoutModal, setShowWorkoutModal] = useState(false);
  const [savingWorkout, /* setSavingWorkout */] = useState(false);
  const [editableWorkout, setEditableWorkout] = useState<WorkoutData | null>(null);
  const [detectedMeal, setDetectedMeal] = useState<MealData | null>(null);
  const [showMealModal, setShowMealModal] = useState(false);
  const [savingMeal, setSavingMeal] = useState(false);
  const [editableMeal, setEditableMeal] = useState<MealData | null>(null); // Use MealData
  const [activeMessage, setActiveMessage] = useState<ExtendedMessage | null>(null);
  const [showInputModal, setShowInputModal] = useState(false);
  const [modalInput, setModalInput] = useState('');
  const [showHistoryModal, setShowHistoryModal] = useState(false);

  // State for recommended activities
  const [recommendedMeal, setRecommendedMeal] = useState<Recommendation | null>(null); // Use Recommendation type
  const [recommendedWorkout, setRecommendedWorkout] = useState<Recommendation | null>(null); // Use Recommendation type
  const [loadingRecommendations, setLoadingRecommendations] = useState(true);

  // Function to refresh recommendations (clear cache and fetch new ones)
  const refreshRecommendations = async () => {
    try {
      await clearRecommendationCache();
      await fetchRecommendations();
      console.log("Refreshed recommendations from daily digest");
    } catch (error) {
      console.error("Error refreshing recommendations:", error);
    }
  };

  // Function to fetch recommendations
  const fetchRecommendations = async () => {
    console.log("Fetching recommendations...");
    setLoadingRecommendations(true);
    try {
      // First try to get cached recommendations from AsyncStorage
      const cachedMealJson = await AsyncStorage.getItem('cached_next_meal');
      const cachedWorkoutJson = await AsyncStorage.getItem('cached_next_workout');

      let meal = null;
      let workout = null;

      // Parse cached data if available
      if (cachedMealJson) {
        try {
          meal = JSON.parse(cachedMealJson);
          console.log("Using cached meal recommendation");
        } catch (e) {
          console.error("Error parsing cached meal:", e);
        }
      }

      if (cachedWorkoutJson) {
        try {
          workout = JSON.parse(cachedWorkoutJson);
          console.log("Using cached workout recommendation");
        } catch (e) {
          console.error("Error parsing cached workout:", e);
        }
      }

      // If no cached data, fetch from API
      if (!meal) {
        console.log("No cached meal, fetching from API");
        meal = await getNextMeal();
        // Cache the result
        if (meal) {
          await AsyncStorage.setItem('cached_next_meal', JSON.stringify(meal));
        }
      }

      if (!workout) {
        console.log("No cached workout, fetching from API");
        workout = await getNextWorkout();
        // Cache the result
        if (workout) {
          await AsyncStorage.setItem('cached_next_workout', JSON.stringify(workout));
        }
      }

      setRecommendedMeal(meal);
      setRecommendedWorkout(workout);

    } catch (error) {
      console.error("Error fetching recommendations:", error);
      setRecommendedMeal(null);
      setRecommendedWorkout(null);
    } finally {
      setLoadingRecommendations(false);
    }
  };

  // Fetch recommendations on component mount
  useEffect(() => {
    fetchRecommendations();

    // Set up a refresh interval (every 5 minutes to catch digest updates)
    const refreshInterval = setInterval(() => {
      refreshRecommendations();
    }, 5 * 60 * 1000);

    return () => clearInterval(refreshInterval);
  }, []);

  // Refresh recommendations when the screen comes into focus
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      refreshRecommendations();
    });

    return unsubscribe;
  }, [navigation]);


  // Renders a conversation item in the conversation list
  const renderLogItem = useCallback(({ item }: { item: any }) => {
    return (
      <TouchableOpacity
        style={{
          backgroundColor: colors.card,
          borderRadius: 12,
          padding: 12,
          marginRight: 8,
          marginBottom: 8,
          width: 160,
          height: 100,
          justifyContent: 'center',
          alignItems: 'flex-start',
        }}
        onPress={() => {
          setCurrentConversation(item);
          setMessages(item.messages || []);
        }}
      >
        <Text style={{ color: colors.text, fontWeight: 'bold' }}>
          {item.description || 'Conversation'}
        </Text>
        <Text style={{ color: colors.textSecondary, fontSize: 12, marginTop: 4 }}>
          {item.timestamp ? new Date(item.timestamp).toLocaleString() : ''}
        </Text>
      </TouchableOpacity>
    );
  }, [colors, setCurrentConversation, setMessages]);



  // Add state for API URL
  const [apiUrl, setApiUrl] = useState('');
  const [newApiUrl, setNewApiUrl] = useState('');

  // Add state for API URL diagnostics
  const [apiUrlInfo, setApiUrlInfo] = useState<{
    url: string;
    host: string;
    port: string | null;
    protocol: string;
    status: 'checking' | 'ok' | 'error';
    error?: string;
  } | null>(null);

  // Function to load the current API URL
  const loadApiUrl = async () => {
    try {
      const url = await getApiUrl();
      setApiUrl(url);
      setNewApiUrl(url);
    } catch (error: any) {
      console.error('Error loading API URL:', error);
    }
  };

  // Function to update the API URL
  const updateApiUrl = async () => {
    try {
      if (newApiUrl && newApiUrl !== apiUrl) {
        await setApiUrlOverride(newApiUrl);
        Alert.alert('API URL Updated', 'You may need to restart the app for changes to take effect.', [
          { text: 'OK' }
        ]);
        await loadApiUrl();
      }
    } catch (error: any) {
      console.error('Error updating API URL:', error);
    }
  };

  // Function to check API URL components
  const checkApiUrlComponents = async () => {
    try {
      const info = await checkApiUrl();
      setApiUrlInfo(info);
      console.log('[DEBUG] API URL components:', info);
    } catch (error: any) {
      console.error('[DEBUG] Error checking API URL:', error);
      setApiUrlInfo({
        url: 'error',
        host: 'error',
        port: null,
        protocol: 'error',
        status: 'error',
        error: error.message || 'Unknown error'
      });
    }
  };

  // --- Helper Functions ---
  const scrollToBottom = useCallback((animated = true) => {
    if (!scrollViewRef.current || messages.length === 0) return;

    // Improved scrolling with better timing and error handling
    try {
      // First immediate scroll without animation to ensure we're at the bottom
      scrollViewRef.current.scrollToEnd({ animated: false });

      // Then do a smooth animated scroll for better UX
      if (animated) {
        // Use requestAnimationFrame to ensure the scroll happens after layout
        requestAnimationFrame(() => {
          if (scrollViewRef.current) {
            scrollViewRef.current.scrollToEnd({ animated: true });
          }
        });
      }

      // Add a backup scroll with a slight delay to catch any late layout changes
      setTimeout(() => {
        if (scrollViewRef.current) {
          scrollViewRef.current.scrollToEnd({ animated });
        }
      }, 100);

      // Final safety scroll with longer delay for complex layouts or slow devices
      setTimeout(() => {
        if (scrollViewRef.current) {
          scrollViewRef.current.scrollToEnd({ animated: false });
        }
      }, 300);
    } catch (e) {
      // Just catch and ignore errors - they're usually harmless scroll attempts
      // that happen when the component is being updated
      console.log('Scroll error (safe to ignore):', e);
    }
  }, [messages.length]);

  // Set up keyboard listeners to scroll when keyboard appears
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      () => {
        // Scroll to bottom when keyboard appears
        setTimeout(() => scrollToBottom(true), 100);
      }
    );

    return () => {
      keyboardDidShowListener.remove();
    };
  }, [scrollToBottom]);

  const dismissKeyboard = () => { Keyboard.dismiss(); };

  // Function to handle text input
  const handleTextInputChange = useCallback((text: string) => {
    setInput(text);
  }, []);

  const formatTimestamp = (timestamp: string): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  const animateButtonPress = () => {
    Animated.sequence([
      Animated.timing(buttonScale, {
        toValue: 0.9,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(buttonScale, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const validateWorkoutData = (workout: any): WorkoutData => { return workout as WorkoutData; };
  const validateMealData = (meal: any): MealData => { return meal as MealData; };

  // Helper function to format workout data for display
  const formatWorkoutValue = (value: any): string => {
    if (value === null || value === undefined) return '';

    // If it's a simple string or number, return as is
    if (typeof value === 'string' || typeof value === 'number') return String(value);

    // If it's an array of objects (like sets with reps/weight)
    if (Array.isArray(value)) {
      return value.map((item, index) => {
        if (typeof item === 'object') {
          const parts: string[] = [];

          // Handle common properties in workout sets
          if (item.reps !== undefined) parts.push(`${item.reps} reps`);
          if (item.weight !== undefined) parts.push(`${item.weight}${typeof item.weight === 'number' ? ' lbs' : ''}`);
          if (item.duration !== undefined) parts.push(`${item.duration}`);

          return `Set ${index + 1}: ${parts.join(', ')}`;
        }
        return `Set ${index + 1}: ${item}`;
      }).join('\n');
    }

    // If it's an object but not an array
    if (typeof value === 'object') {
      const parts: string[] = [];
      Object.entries(value).forEach(([key, val]) => {
        parts.push(`${key}: ${val}`);
      });
      return parts.join(', ');
    }

    // Fallback
    return JSON.stringify(value);
  };

  // --- Core Logic Functions ---

  // Moved loadConversation definition BEFORE loadOrCreateConversation (Fixes scope issue)
  const loadConversation = useCallback(async (conversationId: string) => {
    console.log(`[loadConversation] Loading conversation ${conversationId}`);
    try {
        setIsProcessing(true);
        let conversation = getConversationById(conversationId);
        if (conversation) {
            console.log(`[loadConversation] Found ${conversationId} in context.`);
            setCurrentConversation(conversation);
            setMessages(conversation.messages || []);
            refreshConversationMessages(conversationId);
        } else {
            console.log(`[loadConversation] ${conversationId} not in context, fetching from service.`);
            const logFromStorage = await getLogById(conversationId); // Use imported function
            if (logFromStorage && logFromStorage.type === 'conversation') {
                console.log(`[loadConversation] Loaded ${conversationId} from storage.`);
                setCurrentConversation(logFromStorage);
                setMessages(logFromStorage.messages || []);
                refreshConversationMessages(conversationId);
            } else {
                 console.error(`[loadConversation] Conversation ${conversationId} not found.`);
                 Alert.alert("Error", "Conversation not found.");
                 setCurrentConversation(null); setMessages([]);
            }
        }
    } catch (error) {
        console.error(`Error loading conversation ${conversationId}:`, error);
        Alert.alert("Error", "Failed to load conversation.");
    } finally {
        setIsProcessing(false);
        // Use multiple scroll attempts with increasing delays to ensure content is fully rendered
        setTimeout(() => scrollToBottom(false), 100);
        setTimeout(() => scrollToBottom(true), 300);
        setTimeout(() => scrollToBottom(true), 500);
    }
  }, [getConversationById, refreshConversationMessages]);

  // loadOrCreateConversation (Uses loadConversation)
  const loadOrCreateConversation = useCallback(async () => {
    console.log("[loadOrCreateConversation] Called");
    if (isLoading) return;
    const sortedConversations = [...conversations].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    const latestConversation = sortedConversations.length > 0 ? sortedConversations[0] : null;
    if (latestConversation) {
      console.log(`[loadOrCreateConversation] Loading latest: ${latestConversation.id}`);
      await loadConversation(latestConversation.id);
    } else {
      console.log("[loadOrCreateConversation] No conversations, creating new one.");
      const newConv = await createConversation('First Conversation');
      if (newConv) {
        await loadConversation(newConv.id);
      } else {
        console.error("[loadOrCreateConversation] Failed to create initial conversation.");
      }
    }
  }, [isLoading, createConversation, loadConversation]);

  // startNewConversation (Uses createConversation, addMessage from context)
  const startNewConversation = async () => {
    console.log('[startNewConversation] Starting new conversation...');
    setIsProcessing(true);
    setCurrentConversation(null); setMessages([]);
    setDetectedWorkout(null); setDetectedMeal(null);
    try {
      if (!tokens) throw new Error("Authentication error.");
      const newConversation = await createConversation();
      if (newConversation?.id) {
        setCurrentConversation(newConversation); setMessages([]);
        const welcomeMessage: Message = {
          content: "Hello! How can I help you today?", role: 'assistant',
          timestamp: new Date().toISOString(),
        };
        const added = await addMessage(newConversation.id, welcomeMessage);
        if (added) {
          setMessages([welcomeMessage]);
          // Refresh recommendations when starting a new conversation
          refreshRecommendations();
        } else {
          Alert.alert("Error", "Failed to initialize conversation.");
        }
      } else {
        Alert.alert("Error", "Could not start a new conversation.");
      }
    } catch (error: any) {
      console.error("[startNewConversation] Error:", error);
      Alert.alert("Error", error.message || "An unexpected error occurred.");
    } finally {
      setIsProcessing(false);
    }
  };

  // updateConversationDescription (Uses saveLog from service, getConversationById from context)
  const updateConversationDescription = async (conversationId: string, description: string) => {
    if (!description) return;

    // Extract a better title from user messages
    let enhancedDescription = description;

    try {
      const logToSave = getConversationById(conversationId);
      if (logToSave) {
        if (logToSave.messages && logToSave.messages.length >= 1) {
          // If we have messages, try to extract a better title from the first user message
          const userMessages = logToSave.messages.filter(msg => msg.role === 'user');
          if (userMessages.length > 0) {
            const firstUserMessage = userMessages[0].content;

            // Extract the first sentence or up to 50 chars for the title
            let extractedTitle = firstUserMessage;
            const sentenceEndMatch = firstUserMessage.match(/[.!?]/);

            if (sentenceEndMatch && typeof sentenceEndMatch.index === 'number' && sentenceEndMatch.index < 100) {
              // If we found end of first sentence and it's reasonably short, use that
              extractedTitle = firstUserMessage.substring(0, sentenceEndMatch.index + 1);
            }

            // Ensure the title is not too long
            if (extractedTitle.length > 50) {
              extractedTitle = extractedTitle.substring(0, 47) + '...';
            }

            enhancedDescription = extractedTitle;
          }
        }

        // Save the updated description
        await saveLog({ ...logToSave, description: enhancedDescription });
        console.log(`[updateDesc] Updated ${conversationId} with enhanced description`);
      } else {
        console.warn(`[updateDesc] Could not find log ${conversationId}.`);
      }
    } catch (error) {
      console.error(`Error updating desc for ${conversationId}:`, error);
    }
  };

  // Define handleMessageAction BEFORE renderMessage
  const handleMessageAction = (message: ExtendedMessage) => {
     setActiveMessage(message);
     if (message.detectedWorkout) {
         const workoutCopy = JSON.parse(JSON.stringify(message.detectedWorkout));
         setEditableWorkout(workoutCopy);
         setShowWorkoutModal(true);
      }
     else if (message.detectedMeal) {
         console.log('Opening meal data:', JSON.stringify(message.detectedMeal, null, 2));
         setEditableMeal(message.detectedMeal);
         setShowMealModal(true);
      }
  };

  // Renders a chat message in the messages list
  const renderMessage = useCallback((item: any) => {
    // Safety check to prevent "Cannot read property 'role' of undefined" error
    if (!item) {
      console.warn('Attempted to render undefined message');
      return null;
    }

    const isUser = item.role === 'user';
    const hasDetectedMeal = !!item.detectedMeal;
    const hasDetectedWorkout = !!item.detectedWorkout;
    const isLoading = !!item.isLoading;

    // If this is a loading message, render the ChatLoadingIndicator
    if (isLoading && !isUser) {
      return <ChatLoadingIndicator />;
    }

    return (
      <View style={{ marginVertical: 4 }}>
        <View
          style={{
            alignSelf: isUser ? 'flex-end' : 'flex-start',
            backgroundColor: isUser ? colors.primary : colors.card,
            borderRadius: 16,
            padding: 10,
            maxWidth: '80%',
          }}
        >
          <Text style={{ color: isUser ? '#fff' : colors.text }}>{item.content}</Text>
        </View>

        {/* Display meal button if meal data is detected */}
        {hasDetectedMeal && !isLoading && (
          <TouchableOpacity
            style={{
              alignSelf: isUser ? 'flex-end' : 'flex-start',
              backgroundColor: isUser ? 'rgba(255,255,255,0.15)' : colors.primaryLight + '15',
              borderColor: isUser ? 'rgba(255,255,255,0.3)' : colors.primary,
              borderWidth: 1,
              marginTop: 8,
              borderRadius: 14,
              padding: 12,
              maxWidth: '80%',
            }}
            onPress={() => handleMessageAction(item)}
            activeOpacity={0.7}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Ionicons
                name="restaurant"
                size={18}
                color={colors.primary}
                style={{ marginRight: 8 }}
              />
              <Text style={{
                fontWeight: '600',
                color: colors.primary,
                flex: 1,
                fontSize: 15
              }}>
                View Recipe: {item.detectedMeal?.title || 'Recipe'}
              </Text>
              <Ionicons
                name="chevron-forward"
                size={18}
                color={colors.primary}
              />
            </View>
          </TouchableOpacity>
        )}

        {/* Display workout button if workout data is detected */}
        {hasDetectedWorkout && !isLoading && (
          <TouchableOpacity
            style={{
              alignSelf: isUser ? 'flex-end' : 'flex-start',
              backgroundColor: isUser ? 'rgba(255,255,255,0.15)' : colors.primaryLight + '15',
              borderColor: isUser ? 'rgba(255,255,255,0.3)' : colors.primary,
              borderWidth: 1,
              marginTop: 8,
              borderRadius: 14,
              padding: 12,
              maxWidth: '80%',
            }}
            onPress={() => handleMessageAction(item)}
            activeOpacity={0.7}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Ionicons
                name="barbell"
                size={18}
                color={colors.primary}
                style={{ marginRight: 8 }}
              />
              <Text style={{
                fontWeight: '600',
                color: colors.primary,
                flex: 1,
                fontSize: 15
              }}>
                View Workout: {item.detectedWorkout?.title || 'Workout Plan'}
              </Text>
              <Ionicons
                name="chevron-forward"
                size={18}
                color={colors.primary}
              />
            </View>
          </TouchableOpacity>
        )}
      </View>
    );
  }, [colors, handleMessageAction]);

  // deleteConversation (Uses deleteConversationFromContext, loadConversation, startNewConversation)
  const deleteConversation = async (conversationId: string) => {
    console.log(`[deleteConversation] Deleting ${conversationId}`);
    try {
      const deleted = await deleteConversationFromContext(conversationId);
      if (deleted) {
        console.log(`[deleteConversation] Success for ${conversationId}.`);
        if (currentConversation?.id === conversationId) {
            setCurrentConversation(null); setMessages([]);
            const remainingConversations = conversations.filter(c => c.id !== conversationId)
                                                  .sort((a,b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
            if (remainingConversations.length > 0) {
                await loadConversation(remainingConversations[0].id);
            } else {
                await startNewConversation();
            }
        }
        // else: Deletion was successful but not the current convo, context state updates list automatically
      } else {
         console.error(`[deleteConversation] Failed to delete conversation ${conversationId} via context.`);
         Alert.alert("Error", "Failed to delete conversation.");
      }
    } catch (error) {
      console.error(`Error deleting conversation ${conversationId}:`, error);
      Alert.alert("Error", "An unexpected error occurred while deleting.");
    }
  };

  // Helper function to handle submission errors
  const handleSubmissionError = (error: any, conversationId: string | undefined) => {
    console.error('[CRITICAL ERROR] Error processing message:', error);

    // Create a user-friendly error message
    let errorContent = 'Sorry, I encountered a problem processing your message.';

    if (error.message?.includes('network') || error.code === 'ECONNABORTED') {
      errorContent = 'Network error. Please check your connection and try again.';
    } else if (error.response?.status === 429) {
      errorContent = 'Too many requests. Please wait a moment before trying again.';
    } else if (error.response?.status >= 500) {
      errorContent = 'Server error. Our team has been notified and is working on it.';
    }

    // Create error message object
    const errorMessage: ExtendedMessage = {
      content: errorContent,
      role: 'assistant',
      timestamp: new Date().toISOString(),
      isLoading: false,
    };

    // Update UI, removing any loading indicators
    setMessages(prev => [...prev.filter(m => !('isLoading' in m) || !m.isLoading), errorMessage]);

    // Save the error message to the conversation
    if (conversationId) {
      try {
        addMessage(conversationId, errorMessage);
      } catch (saveError) {
        console.error("Failed to save error message to conversation:", saveError);
      }
    }

    setIsProcessing(false);
    scrollToBottom(true);

    // Show an alert to the user
    Alert.alert(
      "Message Error",
      "There was a problem sending your message. Please try again later.",
      [{ text: "OK" }]
    );
  };

  // Direct message submission handler - takes a message object directly
  const handleSubmitDirect = async (userMessage: ExtendedMessage) => {
    // Implementation for direct message submission
    if (isProcessing) return;

    setIsProcessing(true);
    Keyboard.dismiss();

    // Add the message to the UI immediately
    setMessages(prev => [...prev, userMessage]);

    // Set isAtEnd to true to ensure we stay at the bottom
    setIsAtEnd(true);

    // Improved scrolling sequence with better timing
    // First immediate scroll without animation
    scrollToBottom(false);

    // Use requestAnimationFrame for smoother scrolling
    requestAnimationFrame(() => {
      scrollToBottom(true);

      // Add multiple backup scrolls with increasing delays
      setTimeout(() => scrollToBottom(true), 50);
      setTimeout(() => scrollToBottom(true), 150);
      setTimeout(() => scrollToBottom(true), 300);
      setTimeout(() => scrollToBottom(true), 500);
    });

    // Target conversation (existing or to be created)
    let conversationId = currentConversation?.id;

    try {
      // Extract user ID from auth token
      const decodedToken = tokens?.idToken ? decodeJwt(tokens.idToken) : null;
      const userId = decodedToken?.sub;
      if (!userId) throw new Error("User ID not found in token.");

      // Create conversation if needed
      if (!conversationId) {
        const initialTitle = userMessage.content.length > 30
          ? userMessage.content.substring(0, 30) + '...'
          : userMessage.content;

        const newConv = await createConversation(initialTitle);
        if (!newConv?.id) throw new Error("Failed to create conversation.");
        conversationId = newConv.id;
        setCurrentConversation(newConv);

        // Add user message to new conversation
        await addMessage(conversationId, userMessage);
      } else {
        // Add message to existing conversation
        await addMessage(conversationId, userMessage);
      }

      // Continue with the rest of the submission process
      await processAssistantResponse(conversationId, userId, [...messages, userMessage]);

    } catch (error: any) {
      console.error('Error in handleSubmitDirect:', error);
      handleSubmissionError(error, conversationId);
    }
  };

  // Helper function to process assistant response
  const processAssistantResponse = async (conversationId: string, userId: string, messageHistory: ExtendedMessage[]) => {
    // Add loading indicator with empty content (will be replaced by ChatLoadingIndicator)
    const loadingMessage: ExtendedMessage = {
      content: '',
      role: 'assistant',
      timestamp: new Date().toISOString(),
      isLoading: true,
    };

    setMessages(prev => [...prev, loadingMessage]);

    // Set isAtEnd to true to ensure we stay at the bottom
    setIsAtEnd(true);

    // Enhanced scrolling sequence for loading message
    // First immediate scroll without animation
    scrollToBottom(false);

    // Use requestAnimationFrame for smoother scrolling
    requestAnimationFrame(() => {
      // Animated scroll in the next frame
      scrollToBottom(true);

      // Multiple backup scrolls with increasing delays
      setTimeout(() => scrollToBottom(true), 50);
      setTimeout(() => scrollToBottom(true), 150);
      setTimeout(() => scrollToBottom(true), 300);
    });

    try {
      // Prepare context for Chat API (use most recent messages for context)
      const conversationHistory = messageHistory.slice(-10);

      // Send message to Chat API
      const response = await sendMessageToChat(
        conversationHistory,
        userId,
        conversationId
      );

      // Create the assistant message using the response data
      const assistantMessage: ExtendedMessage = {
        content: response.content,
        role: 'assistant',
        timestamp: new Date().toISOString(),
        detectedWorkout: response.detectedWorkout,
        detectedMeal: response.detectedMeal,
        isLoading: false,
      };

      // Log structured data detection if present
      if (assistantMessage.detectedMeal) {
        console.log('Meal data detected:', assistantMessage.detectedMeal.title);
      }
      if (assistantMessage.detectedWorkout) {
        console.log('Workout data detected:', assistantMessage.detectedWorkout.title);
      }

      // Update UI with the new message (remove loading indicator)
      setMessages(prev => {
        const newMessages = [...prev.filter(m => !('isLoading' in m) || !m.isLoading), assistantMessage];
        return newMessages;
      });

      // Save the message to persistent storage
      addMessage(conversationId, assistantMessage);

      // Ensure proper scrolling to new content with enhanced reliability
      setIsAtEnd(true);

      // Immediate scroll without animation first
      scrollToBottom(false);

      // Use a more comprehensive sequence of scroll attempts with better timing
      requestAnimationFrame(() => {
        // Scroll in the next frame
        scrollToBottom(true);

        // Then add multiple backup scrolls with increasing delays
        // This ensures the content is visible even if layout changes occur
        setTimeout(() => scrollToBottom(true), 100);
        setTimeout(() => scrollToBottom(true), 300);
        setTimeout(() => scrollToBottom(true), 500);
        setTimeout(() => scrollToBottom(true), 800);
        setTimeout(() => scrollToBottom(true), 1200);
      });

      // Store detected structured data for potential user interaction
      if (response.detectedWorkout) {
            setDetectedWorkout(response.detectedWorkout);
            setEditableWorkout(response.detectedWorkout);
        }
        if (response.detectedMeal) {
            setDetectedMeal(response.detectedMeal);
            setEditableMeal(response.detectedMeal);
        }

        // Update conversation title/description based on user's first message
        if (messages.length <= 3) {
            // Use the user's first message for a more meaningful title
            const userMessages = messages.filter(m => m.role === 'user');
            if (userMessages.length > 0) {
                updateConversationDescription(conversationId, userMessages[0].content);
            }
        }

        // Mark processing as complete
        setIsProcessing(false);

    } catch (error: any) {
        // Clear the loading update timeout
        if (loadingUpdateTimeoutRef.current) {
          clearTimeout(loadingUpdateTimeoutRef.current);
          loadingUpdateTimeoutRef.current = undefined;
        }

        console.error('[CRITICAL ERROR] Error processing message:', error);
        console.error('[CRITICAL ERROR] Error stack:', error.stack);

        // Create a user-friendly error message
        let errorContent = 'Sorry, I encountered a problem processing your message.';

        if (error.message?.includes('network') || error.code === 'ECONNABORTED') {
            errorContent = 'Network error. Please check your connection and try again.';
        } else if (error.response?.status === 429) {
            errorContent = 'Too many requests. Please wait a moment before trying again.';
        } else if (error.response?.status >= 500) {
            errorContent = 'Server error. Our team has been notified and is working on it.';
        }

        // Create error message object
        const errorMessage: ExtendedMessage = {
            content: errorContent,
            role: 'assistant',
            timestamp: new Date().toISOString(),
            isLoading: false,
        };

        // Update UI, removing any loading indicators
        setMessages(prev => [...prev.filter(m => !('isLoading' in m) || !m.isLoading), errorMessage]);

        // Save the error message to the conversation
        if (conversationId) {
          try {
            await addMessage(conversationId, errorMessage);
          } catch (saveError) {
            console.error("Failed to save error message to conversation:", saveError);
          }
        }

        // Note: Haptic feedback disabled until expo-haptics is installed
        // Simple vibration for error feedback on Android
        try {
          if (Platform.OS === 'android') {
            Vibration.vibrate(100); // Longer error vibration
          }
        } catch (e) {
          // Ignore if vibration not available
        }

        setIsProcessing(false);
        scrollToBottom(true);

        // Show an alert to the user
        Alert.alert(
          "Message Error",
          "There was a problem sending your message. Please try again later.",
          [{ text: "OK" }]
        );
    }
  };

  // Render meal modal
  const renderMealModal = () => {
    if (!editableMeal) return null;

    return (
      <Modal
        visible={showMealModal}
        animationType="none"
        transparent={true}
        onRequestClose={() => setShowMealModal(false)}
      >
        <ReAnimated.View
          entering={FadeIn.duration(300)}
          exiting={FadeOut.duration(200)}
          style={[styles.mealModalContainer, { backgroundColor: 'rgba(0,0,0,0.5)' }]}
        >
          <ReAnimated.View
            entering={SlideInDown.duration(400).springify()}
            exiting={FadeOut.duration(200)}
            style={[styles.modalContent, { backgroundColor: colors.background }]}
          >
            <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>{editableMeal.title}</Text>
              <TouchableOpacity 
                onPress={() => setShowMealModal(false)}
                style={[styles.closeButton, { backgroundColor: colors.surface }]}
              >
                <Ionicons name="close" size={20} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalScrollView}>
              {editableMeal.description && (
                <View style={[styles.modalSection, { borderBottomColor: colors.border }]}>
                  <View style={styles.sectionTitleContainer}>
                    <View style={[styles.sectionAccent, { backgroundColor: '#FF9632' }]} />
                    <Text style={[styles.sectionTitle, { color: colors.text }]}>Description</Text>
                  </View>
                  <Text style={[styles.sectionContent, { color: colors.textSecondary }]}>
                    {editableMeal.description}
                  </Text>
                </View>
              )}

              {editableMeal.ingredients && editableMeal.ingredients.length > 0 && (
                <View style={[styles.modalSection, { borderBottomColor: colors.border }]}>
                  <View style={styles.sectionTitleContainer}>
                    <View style={[styles.sectionAccent, { backgroundColor: '#34C759' }]} />
                    <Text style={[styles.sectionTitle, { color: colors.text }]}>Ingredients</Text>
                  </View>
                  {editableMeal.ingredients.map((ingredient, index) => (
                    <View key={index} style={[styles.ingredientItem, { backgroundColor: colors.surfaceLight, borderRadius: 8 }]}>
                      <Text style={[styles.ingredientText, { color: colors.text }]}>
                        • {ingredient}
                      </Text>
                    </View>
                  ))}
                </View>
              )}

              {editableMeal.steps && editableMeal.steps.length > 0 && (
                <View style={[styles.modalSection, { borderBottomColor: colors.border }]}>
                  <View style={styles.sectionTitleContainer}>
                    <View style={[styles.sectionAccent, { backgroundColor: '#007AFF' }]} />
                    <Text style={[styles.sectionTitle, { color: colors.text }]}>Instructions</Text>
                  </View>
                  {editableMeal.steps.map((step, index) => (
                    <View key={index} style={styles.stepItem}>
                      <Text style={[styles.stepNumber, { color: '#fff', backgroundColor: colors.primary }]}>{index + 1}</Text>
                      <Text style={[styles.stepText, { color: colors.text }]}>{step}</Text>
                    </View>
                  ))}
                </View>
              )}

              {/* Display nutrition facts if available */}
              {(editableMeal.calories || editableMeal.protein || editableMeal.carbs || editableMeal.fat) ? (
                <View style={[styles.modalSection, { borderBottomColor: colors.border }]}>
                  <View style={styles.sectionTitleContainer}>
                    <View style={[styles.sectionAccent, { backgroundColor: '#FF9500' }]} />
                    <Text style={[styles.sectionTitle, { color: colors.text }]}>Nutrition Facts</Text>
                  </View>
                  <View style={styles.nutritionGrid}>
                    {editableMeal.calories && (
                      <View style={[styles.nutritionItem, { backgroundColor: colors.surfaceLight }]}>
                        <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Calories</Text>
                        <Text style={[styles.nutritionValue, { color: colors.text }]}>{editableMeal.calories}</Text>
                      </View>
                    )}
                    {editableMeal.protein && (
                      <View style={[styles.nutritionItem, { backgroundColor: colors.surfaceLight }]}>
                        <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Protein</Text>
                        <Text style={[styles.nutritionValue, { color: colors.text }]}>{editableMeal.protein}g</Text>
                      </View>
                    )}
                    {editableMeal.carbs && (
                      <View style={[styles.nutritionItem, { backgroundColor: colors.surfaceLight }]}>
                        <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Carbs</Text>
                        <Text style={[styles.nutritionValue, { color: colors.text }]}>{editableMeal.carbs}g</Text>
                      </View>
                    )}
                    {editableMeal.fat && (
                      <View style={[styles.nutritionItem, { backgroundColor: colors.surfaceLight }]}>
                        <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Fat</Text>
                        <Text style={[styles.nutritionValue, { color: colors.text }]}>{editableMeal.fat}g</Text>
                      </View>
                    )}
                  </View>
                </View>
              ) : null}
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: colors.primary }]}
                onPress={async () => {
                  try {
                    setSavingMeal(true);
                    await saveMealFromConversation(editableMeal);
                    Alert.alert('Success', 'Meal saved successfully!');
                    setShowMealModal(false);
                  } catch (error) {
                    console.error('Error saving meal:', error);
                    Alert.alert('Error', 'Failed to save meal');
                  } finally {
                    setSavingMeal(false);
                  }
                }}
                disabled={savingMeal}
              >
                {savingMeal ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Text style={styles.buttonText}>Save Meal</Text>
                )}
              </TouchableOpacity>
            </View>
          </ReAnimated.View>
        </ReAnimated.View>
      </Modal>
    );
  };

  // Render workout modal
  const renderWorkoutModal = () => {
    if (!editableWorkout) return null;

    return (
      <Modal
        visible={showWorkoutModal}
        animationType="none"
        transparent={true}
        onRequestClose={() => setShowWorkoutModal(false)}
      >
        <ReAnimated.View
          entering={FadeIn.duration(300)}
          exiting={FadeOut.duration(200)}
          style={[styles.mealModalContainer, { backgroundColor: 'rgba(0,0,0,0.5)' }]}
        >
          <ReAnimated.View
            entering={SlideInDown.duration(400).springify()}
            exiting={FadeOut.duration(200)}
            style={[styles.modalContent, { backgroundColor: colors.background }]}
          >
            <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>{editableWorkout.title}</Text>
              <TouchableOpacity 
                onPress={() => setShowWorkoutModal(false)}
                style={[styles.closeButton, { backgroundColor: colors.surface }]}
              >
                <Ionicons name="close" size={20} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalScrollView}>
              <View style={[styles.modalSection, { borderBottomColor: colors.border }]}>
                <View style={styles.sectionTitleContainer}>
                  <View style={[styles.sectionAccent, { backgroundColor: '#5082FF' }]} />
                  <Text style={[styles.sectionTitle, { color: colors.text }]}>Workout Details</Text>
                </View>

                {editableWorkout.description && (
                  <Text style={[styles.sectionContent, { color: colors.textSecondary, marginBottom: 10 }]}>
                    {editableWorkout.description}
                  </Text>
                )}

                <View style={{ flexDirection: 'row', flexWrap: 'wrap', marginTop: 8 }}>
                  {editableWorkout.type && (
                    <View style={{
                      backgroundColor: colors.primaryLight + '30',
                      paddingHorizontal: 10,
                      paddingVertical: 4,
                      borderRadius: 12,
                      marginRight: 8,
                      marginBottom: 8
                    }}>
                      <Text style={{ color: colors.primary, fontSize: 12, fontWeight: '500' }}>
                        {editableWorkout.type}
                      </Text>
                    </View>
                  )}

                  {editableWorkout.difficulty && (
                    <View style={{
                      backgroundColor: colors.primaryLight + '30',
                      paddingHorizontal: 10,
                      paddingVertical: 4,
                      borderRadius: 12,
                      marginRight: 8,
                      marginBottom: 8
                    }}>
                      <Text style={{ color: colors.primary, fontSize: 12, fontWeight: '500' }}>
                        {editableWorkout.difficulty} Difficulty
                      </Text>
                    </View>
                  )}

                  {editableWorkout.duration && (
                    <View style={{
                      backgroundColor: colors.primaryLight + '30',
                      paddingHorizontal: 10,
                      paddingVertical: 4,
                      borderRadius: 12,
                      marginRight: 8,
                      marginBottom: 8
                    }}>
                      <Text style={{ color: colors.primary, fontSize: 12, fontWeight: '500' }}>
                        {editableWorkout.duration}
                      </Text>
                    </View>
                  )}
                </View>
              </View>

              {editableWorkout.exercises && editableWorkout.exercises.length > 0 && (
                <View style={[styles.modalSection, { borderBottomColor: colors.border }]}>
                  <View style={styles.sectionTitleContainer}>
                    <View style={[styles.sectionAccent, { backgroundColor: '#34C759' }]} />
                    <Text style={[styles.sectionTitle, { color: colors.text }]}>Exercises</Text>
                  </View>
                  {editableWorkout.exercises.map((exercise: any, index: number) => (
                    <View key={index} style={[styles.exerciseItem, { borderBottomColor: colors.border, borderBottomWidth: index < editableWorkout.exercises.length - 1 ? 1 : 0, paddingBottom: 12, marginBottom: 12 }]}>
                      <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                        <View style={{ backgroundColor: colors.primaryLight + '30', width: 30, height: 30, borderRadius: 15, alignItems: 'center', justifyContent: 'center', marginRight: 10 }}>
                          <Text style={{ color: colors.primary, fontFamily: typography.fontFamily.semibold }}>{index + 1}</Text>
                        </View>
                        <Text style={{ color: colors.text, fontFamily: typography.fontFamily.semibold, fontSize: 16 }}>
                          {exercise.name}
                        </Text>
                      </View>

                      <View style={{ marginLeft: 40 }}>
                        {exercise.sets && (
                          <View style={{ flexDirection: 'row', marginBottom: 4 }}>
                            <Text style={{ color: colors.textSecondary, width: 80, fontSize: 14 }}>Sets:</Text>
                            <View style={{ flex: 1 }}>
                              {Array.isArray(exercise.sets) ? (
                                exercise.sets.map((set: any, setIndex: number) => (
                                  <Text
                                    key={setIndex}
                                    style={{
                                      color: colors.text,
                                      fontSize: 14,
                                      fontFamily: typography.fontFamily.medium,
                                      marginBottom: setIndex < exercise.sets.length - 1 ? 2 : 0
                                    }}
                                  >
                                    {typeof set === 'object' ? (
                                      `Set ${setIndex + 1}: ${set.reps ? `${set.reps} reps` : ''} ${set.weight ? `at ${set.weight}${typeof set.weight === 'number' ? ' lbs' : ''}` : ''}`
                                    ) : (
                                      `Set ${setIndex + 1}: ${set}`
                                    )}
                                  </Text>
                                ))
                              ) : (
                                <Text style={{ color: colors.text, fontSize: 14, fontFamily: typography.fontFamily.medium }}>
                                  {exercise.sets}
                                </Text>
                              )}
                            </View>
                          </View>
                        )}

                        {exercise.reps && !Array.isArray(exercise.sets) && (
                          <View style={{ flexDirection: 'row', marginBottom: 4 }}>
                            <Text style={{ color: colors.textSecondary, width: 80, fontSize: 14 }}>Reps:</Text>
                            <Text style={{ color: colors.text, fontSize: 14, fontFamily: typography.fontFamily.medium }}>
                              {exercise.reps}
                            </Text>
                          </View>
                        )}

                        {exercise.weight && !Array.isArray(exercise.sets) && (
                          <View style={{ flexDirection: 'row', marginBottom: 4 }}>
                            <Text style={{ color: colors.textSecondary, width: 80, fontSize: 14 }}>Weight:</Text>
                            <Text style={{ color: colors.text, fontSize: 14, fontFamily: typography.fontFamily.medium }}>
                              {exercise.weight}{typeof exercise.weight === 'number' ? ' lbs' : ''}
                            </Text>
                          </View>
                        )}

                        {exercise.duration && (
                          <View style={{ flexDirection: 'row', marginBottom: 4 }}>
                            <Text style={{ color: colors.textSecondary, width: 80, fontSize: 14 }}>Duration:</Text>
                            <Text style={{ color: colors.text, fontSize: 14, fontFamily: typography.fontFamily.medium }}>
                              {exercise.duration}
                            </Text>
                          </View>
                        )}

                        {exercise.notes && (
                          <View style={{ marginTop: 4 }}>
                            <Text style={{ color: colors.textSecondary, fontStyle: 'italic', fontSize: 13 }}>
                              {exercise.notes}
                            </Text>
                          </View>
                        )}
                      </View>
                    </View>
                  ))}
                </View>
              )}
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: colors.primary }]}
                onPress={async () => {
                  try {
                    // No need to set saving state as we're using inline activity indicator
                    // Save workout to logs
                    await saveLog({
                      id: uuidv4(),
                      type: 'workout',
                      timestamp: new Date().toISOString(),
                      description: editableWorkout.title,
                      metrics: {
                        workout: editableWorkout
                      }
                    });
                    Alert.alert('Success', 'Workout saved successfully!');
                    setShowWorkoutModal(false);
                  } catch (error) {
                    console.error('Error saving workout:', error);
                    Alert.alert('Error', 'Failed to save workout');
                  } finally {
                    // No state to reset
                  }
                }}
                disabled={savingWorkout}
              >
                {savingWorkout ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Text style={styles.buttonText}>Save Workout</Text>
                )}
              </TouchableOpacity>
            </View>
          </ReAnimated.View>
        </ReAnimated.View>
      </Modal>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.mainContent}>
        <View
          style={[
            styles.header,
            {
              paddingHorizontal: 20,
              paddingVertical: 15,
              borderBottomColor: colors.border,
              backgroundColor: colors.background,
            }
          ]}
        >
          <Text style={[styles.headerTitle, { color: colors.text }]}>Lotus</Text>
          <View style={styles.headerActions}>
            <TouchableOpacity
              style={[styles.historyButton, { borderColor: colors.primary }]}
              onPress={() => setShowHistoryModal(true)}
            >
              <Ionicons name="time-outline" size={16} color={colors.primary} />
              <Text style={[styles.historyButtonText, { color: colors.primary }]}>History</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: colors.card }]}
              onPress={startNewConversation}
              disabled={isProcessing}
            >
              <Ionicons name="add" size={22} color={colors.primary} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Subtle divider between header and chat */}
        <View
          style={{
            height: 4,
            backgroundColor: 'transparent',
            marginHorizontal: 20,
          }}
        />

        {/* Recommended Activity Widget - Moved to top */}
        {loadingRecommendations ? (
          <SkeletonRecommendedActivity />
        ) : (recommendedMeal || recommendedWorkout) && (
          <RecommendedActivityWidget
            recommendedMeal={recommendedMeal ? {
              ...recommendedMeal?.metadata?.mealDetails,
              title: recommendedMeal?.metadata?.mealDetails?.title || recommendedMeal?.title || "Recommended Meal"
            } : null}
            recommendedWorkout={recommendedWorkout ? {
              ...recommendedWorkout?.metadata?.workoutDetails,
              title: recommendedWorkout?.metadata?.workoutDetails?.title || recommendedWorkout?.title || "Recommended Workout"
            } : null}
            onPressMeal={() => {
              // Create a complete meal object with title from the recommendation
              const mealDetails = recommendedMeal?.metadata?.mealDetails || null;
              if (mealDetails) {
                // Ensure the title is set from the recommendation if not in the details
                const mealWithTitle = {
                  ...mealDetails,
                  title: mealDetails.title || (recommendedMeal ? recommendedMeal.title : null) || "Recommended Meal",
                  // Ensure description is set
                  description: mealDetails.description || (recommendedMeal ? recommendedMeal.description : null) || "",
                  // Ensure ingredients are available
                  ingredients: mealDetails.ingredients || [],
                  // Ensure steps/instructions are available
                  steps: mealDetails.steps || mealDetails.instructions || [],
                  // Ensure nutrition info is available
                  calories: mealDetails.calories || (mealDetails.nutrition?.calories) || null,
                  protein: mealDetails.protein || (mealDetails.nutrition?.protein) || null,
                  carbs: mealDetails.carbs || (mealDetails.nutrition?.carbs) || null,
                  fat: mealDetails.fat || (mealDetails.nutrition?.fat) || null
                };
                setEditableMeal(mealWithTitle);
                console.log("Opening meal modal with:", mealWithTitle);
              } else {
                setEditableMeal(null);
              }
              setShowMealModal(true);
            }}
            onPressWorkout={() => {
              // Create a complete workout object with title from the recommendation
              const workoutDetails = recommendedWorkout?.metadata?.workoutDetails || null;
              if (workoutDetails) {
                // Ensure the title is set from the recommendation if not in the details
                const workoutWithTitle = {
                  ...workoutDetails,
                  title: workoutDetails.title || (recommendedWorkout ? recommendedWorkout.title : null) || "Recommended Workout",
                  // Ensure description is set
                  description: workoutDetails.description || (recommendedWorkout ? recommendedWorkout.description : null) || "",
                  // Ensure exercises are available
                  exercises: workoutDetails.exercises || [],
                  // Ensure duration is set
                  duration: workoutDetails.duration || null,
                  // Ensure type/category is set
                  type: workoutDetails.type || workoutDetails.category || "General",
                  // Ensure difficulty is set
                  difficulty: workoutDetails.difficulty || "Moderate"
                };
                setEditableWorkout(workoutWithTitle);
                console.log("Opening workout modal with:", workoutWithTitle);
              } else {
                setEditableWorkout(null);
              }
              setShowWorkoutModal(true);
            }}
          />
        )}

        <View style={[
          styles.messagesContainer,
          {
            backgroundColor: colors.card + '20', // Slightly different background with transparency
            shadowColor: '#000',
            shadowOffset: { width: 0, height: -2 },
            shadowOpacity: 0.05,
            shadowRadius: 3,
            elevation: 1,
          }
        ]}>
          {messages.length > 0 ? (
            <FlatList
              ref={scrollViewRef}
              data={messages}
              renderItem={({ item }) => renderMessage(item)}
              keyExtractor={(_, index) => `message-${index}`}
              contentContainerStyle={styles.messagesList}
              showsVerticalScrollIndicator={true}
              onContentSizeChange={() => {
                if (isAtEnd) scrollToBottom(true);
              }}
              onLayout={() => {
                if (isAtEnd) scrollToBottom(false);
              }}
              maintainVisibleContentPosition={{
                minIndexForVisible: 0,
                autoscrollToTopThreshold: 10
              }}
              removeClippedSubviews={false}
              initialNumToRender={20}
              maxToRenderPerBatch={10}
              windowSize={10}
            />
          ) : (
            <View style={styles.emptyState}>
              <Ionicons name="chatbubbles-outline" size={40} color={colors.textSecondary} />
              <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
                Start a new conversation
              </Text>
              <ReAnimated.View
                entering={FadeIn.duration(600).delay(300)}
                style={styles.newChatButtonContainer}
              >
                <TouchableOpacity
                  style={[styles.newChatButton, { backgroundColor: colors.primary }]}
                  onPress={() => {
                    // No need to scroll since there are no messages in empty state
                    // Just open the modal
                    setShowInputModal(true);
                  }}
                >
                  <Ionicons name="add" size={32} color="#fff" />
                </TouchableOpacity>
              </ReAnimated.View>
            </View>
          )}
        </View>

        {/* Add a floating action button for new chat when messages exist */}
        {messages.length > 0 && (
          <TouchableOpacity
            style={[styles.floatingActionButton, { backgroundColor: colors.primary }]}
            onPress={() => {
              // Save current scroll position before opening modal
              setIsAtEnd(true);
              // Force scroll to bottom before opening modal to prevent scroll jump
              scrollToBottom(false);
              // Then open the modal
              setShowInputModal(true);
            }}
          >
            <Ionicons name="add" size={24} color="#fff" />
          </TouchableOpacity>
        )}
      </View>

      {/* Render modals */}
      {renderMealModal()}
      {renderWorkoutModal()}

      {/* Chat History Modal */}
      {showHistoryModal && (
        <ReAnimated.View
          style={styles.modalOverlay}
          entering={FadeIn.duration(300)}
          exiting={FadeOut.duration(300)}
        >
          <ReAnimated.View
            style={[styles.historyModalContainer, { backgroundColor: colors.background }]}
            entering={SlideInDown.duration(400).springify()}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 15 }}>
              <Ionicons name="time" size={22} color={colors.primary} style={{ marginRight: 8 }} />
              <Text style={[styles.historyModalTitle, { color: colors.text }]}>Chat History</Text>
            </View>

            {conversations.length > 0 ? (
              <FlatList
                data={conversations.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={[styles.historyItem, { borderBottomColor: colors.border }]}
                    onPress={() => {
                      loadConversation(item.id);
                      setShowHistoryModal(false);
                    }}
                  >
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Ionicons name="chatbubble-outline" size={18} color={colors.primary} style={{ marginRight: 8 }} />
                      <View style={{ flex: 1 }}>
                        <Text style={[styles.historyItemText, { color: colors.text }]}>
                          {item.description || 'Conversation'}
                        </Text>
                        <Text style={[styles.historyItemDate, { color: colors.textSecondary }]}>
                          {new Date(item.timestamp).toLocaleString()}
                        </Text>
                      </View>
                      <Ionicons name="chevron-forward" size={18} color={colors.textSecondary} />
                    </View>
                  </TouchableOpacity>
                )}
              />
            ) : (
              <View style={{ alignItems: 'center', padding: 20 }}>
                <Ionicons name="chatbubbles-outline" size={40} color={colors.textSecondary} style={{ marginBottom: 10 }} />
                <Text style={[styles.emptyHistoryText, { color: colors.textSecondary }]}>
                  No chat history found
                </Text>
              </View>
            )}

            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowHistoryModal(false)}
            >
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
          </ReAnimated.View>
        </ReAnimated.View>
      )}

      {/* Chat Input Modal */}
      {showInputModal && (
        <ReAnimated.View
          style={styles.modalOverlay}
          entering={FadeIn.duration(300)}
          exiting={FadeOut.duration(300)}
        >
          <ReAnimated.View
            style={[styles.modalContainer, { backgroundColor: colors.background }]}
            entering={SlideInDown.duration(400).springify()}
          >
            <View style={styles.modalInputContainer}>
              <TextInput
                style={[styles.modalInput, { color: colors.text, borderColor: colors.border }]}
                placeholder="What would you like to ask?"
                placeholderTextColor={colors.textSecondary}
                value={modalInput}
                onChangeText={setModalInput}
                multiline
                autoFocus
                maxLength={1000}
              />
            </View>
            <TouchableOpacity
              style={[styles.modalSendButton, { backgroundColor: colors.primary }]}
              onPress={() => {
                if (modalInput.trim()) {
                  const userMessage: ExtendedMessage = {
                    role: 'user',
                    content: modalInput.trim(),
                    timestamp: new Date().toISOString(),
                  };
                  setModalInput('');
                  // First close the modal
                  setShowInputModal(false);
                  // Then force scroll to bottom before submitting the message
                  setTimeout(() => {
                    scrollToBottom(false);
                    // Then submit the message after a short delay
                    setTimeout(() => {
                      handleSubmitDirect(userMessage);
                    }, 100);
                  }, 100);
                }
              }}
              disabled={!modalInput.trim()}
            >
              <Text style={styles.modalSendButtonText}>Send</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowInputModal(false)}
            >
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
          </ReAnimated.View>
        </ReAnimated.View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mainContent: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
  },
  newChatButtonContainer: {
    marginTop: 40,
    alignItems: 'center',
  },
  newChatButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  modalOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContainer: {
    width: '85%',
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 20,
    paddingTop: 40, // Extra padding at top to avoid close button overlap
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalInputContainer: {
    width: '100%',
    marginBottom: 20,
  },
  modalInput: {
    width: '100%',
    minHeight: 150, // Increased height
    maxHeight: 250, // Increased max height
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    textAlignVertical: 'top',
    fontFamily: typography.fontFamily.regular,
  },
  modalCloseButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  modalSendButton: {
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 25,
    marginTop: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalSendButtonText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: typography.fontFamily.semibold,
  },
  floatingActionButton: {
    position: 'absolute',
    bottom: Platform.OS === 'ios' ? 80 : 70, // Lowered position to be slightly closer to nav bar
    right: 30,
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
    zIndex: 100,
  },
  historyButton: {
    marginRight: 15,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  historyButtonText: {
    fontSize: 14,
    fontFamily: typography.fontFamily.medium,
    marginLeft: 4,
  },
  historyModalContainer: {
    maxHeight: '70%',
    width: '90%',
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  historyModalTitle: {
    fontSize: 18,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 0,
  },
  historyItem: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  historyItemText: {
    fontSize: 16,
    fontFamily: typography.fontFamily.regular,
  },
  historyItemDate: {
    fontSize: 12,
    fontFamily: typography.fontFamily.regular,
    marginTop: 5,
    opacity: 0.7,
  },
  emptyHistoryText: {
    textAlign: 'center',
    padding: 20,
    fontSize: 16,
    fontFamily: typography.fontFamily.regular,
    opacity: 0.7,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    paddingBottom: 15,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    zIndex: 10,
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.semibold,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  conversationListContainer: {
    paddingVertical: 8,
  },
  conversationList: {
    paddingHorizontal: 16,
  },
  messagesContainer: {
    flex: 1,
    paddingHorizontal: 16,
    backgroundColor: 'transparent',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: 5,
  },
  messagesList: {
    paddingTop: 10,
    paddingBottom: Platform.OS === 'ios' ? 280 : 260, // Further increased padding to ensure messages are always visible
    flexGrow: 1, // Ensure the content container can grow to fill available space
  },
  messageWrapper: {
    marginBottom: 8,
    maxWidth: '85%',
  },
  messageContainer: {
    padding: 12,
    borderRadius: 20,
    maxWidth: '100%',
  },
  userMessage: {
    alignSelf: 'flex-end',
  },
  modelMessage: {
    alignSelf: 'flex-start',
  },
  messageText: {
    fontSize: 16,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
  },
  dataBlock: {
    width: '100%',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyStateText: {
    marginTop: 16,
    fontSize: 16,
    fontFamily: typography.fontFamily.medium,
  },
  // Meal modal styles
  mealModalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modalContent: {
    width: '100%',
    maxWidth: 500,
    maxHeight: '80%',
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    flexDirection: 'column',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 22,
    fontFamily: typography.fontFamily.bold,
    flex: 1,
    letterSpacing: -0.5,
    lineHeight: 28,
  },
  modalScrollView: {
    maxHeight: '70%',
    paddingHorizontal: 0,
  },
  modalSection: {
    padding: 20,
    paddingTop: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: 'rgba(0,0,0,0.08)',
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionAccent: {
    width: 3,
    height: 18,
    borderRadius: 2,
    marginRight: 10,
  },
  sectionTitle: {
    fontSize: 17,
    fontFamily: typography.fontFamily.semibold,
    letterSpacing: -0.2,
  },
  sectionContent: {
    fontSize: 15,
    lineHeight: 22,
    fontFamily: typography.fontFamily.regular,
    opacity: 0.8,
  },
  ingredientItem: {
    marginBottom: 10,
    paddingLeft: 4,
  },
  ingredientText: {
    fontSize: 15,
    lineHeight: 22,
    fontFamily: typography.fontFamily.medium,
    paddingLeft: 8,
  },
  stepItem: {
    flexDirection: 'row',
    marginBottom: 16,
    alignItems: 'flex-start',
  },
  stepNumber: {
    width: 28,
    height: 28,
    borderRadius: 14,
    textAlign: 'center',
    lineHeight: 28,
    marginRight: 12,
    fontFamily: typography.fontFamily.bold,
    fontSize: 14,
    marginTop: 2,
  },
  stepText: {
    flex: 1,
    fontSize: 15,
    lineHeight: 22,
    fontFamily: typography.fontFamily.regular,
  },
  nutritionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
    gap: 8,
  },
  nutritionItem: {
    flex: 1,
    minWidth: '48%',
    padding: 12,
    borderRadius: 10,
    alignItems: 'center',
  },
  nutritionLabel: {
    fontSize: 12,
    fontFamily: typography.fontFamily.medium,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    marginBottom: 4,
    opacity: 0.6,
  },
  nutritionValue: {
    fontSize: 18,
    fontFamily: typography.fontFamily.bold,
    letterSpacing: -0.3,
  },
  modalFooter: {
    padding: 20,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: 'rgba(0,0,0,0.08)',
    alignItems: 'center',
  },
  modalButton: {
    paddingVertical: 14,
    paddingHorizontal: 32,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 140,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: typography.fontFamily.bold,
    letterSpacing: 0.2,
  },
  exerciseItem: {
    marginBottom: 12,
  },
  exerciseName: {
    fontSize: 16,
    fontFamily: typography.fontFamily.semibold,
  },
  exerciseLabel: {
    fontSize: 14,
    fontFamily: typography.fontFamily.regular,
  },
  exerciseValue: {
    fontSize: 14,
    fontFamily: typography.fontFamily.medium,
  },
  exerciseNotes: {
    fontSize: 13,
    fontFamily: typography.fontFamily.regular,
    fontStyle: 'italic',
  },
  inputContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    position: 'absolute', // Use absolute positioning
    bottom: Platform.OS === 'ios' ? 80 : 90, // Position from bottom of screen, above the tab bar
    left: 0,
    right: 0,
    zIndex: 10, // Ensure it's above other elements
    backgroundColor: 'transparent', // Make background transparent
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 24,
    paddingHorizontal: 16,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  input: {
    flex: 1,
    fontSize: 16,
    maxHeight: 100,
    paddingVertical: 8,
    fontFamily: typography.fontFamily.regular,
  },
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
});

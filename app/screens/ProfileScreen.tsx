import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ScrollView,
  ActivityIndicator,
  Switch,
  Animated,
  Platform,
  RefreshControl,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SafeAreaView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { typography } from '../theme/typography';
import { useAuth } from '../context/AuthContext';
import { useProfile } from '../context/ProfileContext';
import { useConversations } from '../context/ConversationContext';
import { useTheme } from '../theme/ThemeProvider';
import { clearLocalWeightEntries, getWeightEntries } from '../services/weightTracking';
import { clearAllLocalCache, getLogs } from '../services/conversationService';
import ProfilePictureUploader from '../components/ProfilePictureUploader';
import AnimatedSettingsItem from '../components/AnimatedSettingsItem';
import ProgressSummaryCard from '../components/ProgressSummaryCard';
import AchievementBadge from '../components/AchievementBadge';
import AchievementsModal from '../components/AchievementsModal';
import UserContextManager from '../components/UserContextManager';
import { useAnimatedTheme } from '../theme/AnimatedThemeProvider';
import { getUserStats, UserStats } from '../services/userStatsService';
import {
  Achievement,
  AchievementStatus,
  getUserAchievements,
  clearAchievementsCache
} from '../services/achievementService';

export default function ProfileScreen() {
  const { signOut, deleteAccount } = useAuth();
  const { profile } = useProfile();
  const { deleteAllConversations } = useConversations();
  const { isDark, setTheme, colors } = useTheme();
  const { animatedColors } = useAnimatedTheme();
  const navigation = useNavigation<NativeStackNavigationProp<any>>();

  // Animation values
  const scrollY = useRef(new Animated.Value(0)).current;
  const headerOpacity = useRef(new Animated.Value(1)).current;

  // State variables
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isContextManagerVisible, setIsContextManagerVisible] = useState(false);
  const [isAchievementsModalVisible, setIsAchievementsModalVisible] = useState(false);

  // User data
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [recentAchievements, setRecentAchievements] = useState<Achievement[]>([]);

  // Progress data
  const [progressData, setProgressData] = useState({
    workouts: { count: 0, trend: 'up', trendValue: '0%' },
    calories: { value: 0, trend: 'neutral', trendValue: '0%' },
    weight: { value: 0, trend: 'down', trendValue: '0%' },
    streak: { days: 0, trend: 'up', trendValue: '0%' },
  });

  // Load profile data
  const loadProfileData = useCallback(async (isRefresh = false) => {
    if (!isRefresh) {
      setIsLoading(true);
    }

    try {
      // Load profile image
      const savedImage = await AsyncStorage.getItem('profilePicture');
      if (savedImage) {
        setProfileImage(savedImage);
      }

      // Load user stats
      const stats = await getUserStats();
      setUserStats(stats);

      // Update progress data based on real stats
      if (stats) {
        const workoutTrend: 'up' | 'neutral' = stats.workouts.thisWeek > 0 ? 'up' : 'neutral';
        const weightTrend: 'down' | 'up' | 'neutral' = stats.weight.change ? (stats.weight.change < 0 ? 'down' : 'up') : 'neutral';
        const caloriesTrend: 'neutral' = 'neutral';
        const streakTrend: 'up' | 'neutral' = stats.activity.currentStreak > 0 ? 'up' : 'neutral';

        setProgressData({
          workouts: {
            count: stats.workouts.thisMonth,
            trend: workoutTrend,
            trendValue: `${stats.workouts.thisWeek} this week`
          },
          calories: {
            value: stats.nutrition.avgCalories,
            trend: caloriesTrend,
            trendValue: `${stats.nutrition.avgProtein}g protein`
          },
          weight: {
            value: stats.weight.current || 0,
            trend: weightTrend as 'up' | 'down' | 'neutral',
            trendValue: stats.weight.changePercentage ? `${stats.weight.changePercentage.toFixed(1)}%` : '0%'
          },
          streak: {
            days: stats.activity.currentStreak,
            trend: streakTrend,
            trendValue: `${stats.activity.longestStreak} longest`
          },
        });
      }

      // Load achievements - force refresh if this is not a pull-to-refresh
      console.log('[ProfileScreen] Loading achievements, isRefresh =', isRefresh);
      const userAchievements = await getUserAchievements(!isRefresh);

      // Log achievements count
      console.log(`[ProfileScreen] Loaded ${userAchievements.length} achievements`);
      console.log('[ProfileScreen] Achievements status breakdown:', {
        unlocked: userAchievements.filter(a => a.status === AchievementStatus.UNLOCKED).length,
        inProgress: userAchievements.filter(a => a.status === AchievementStatus.IN_PROGRESS).length,
        locked: userAchievements.filter(a => a.status === AchievementStatus.LOCKED).length
      });

      // Check if we have a reasonable number of achievements
      if (userAchievements.length < 10) {
        console.warn('[ProfileScreen] Received fewer than 10 achievements, this may indicate an issue');

        // If we have too few achievements, try clearing the cache and fetching again
        try {
          await clearAchievementsCache();
          console.log('[ProfileScreen] Cleared achievements cache, fetching again');

          // Try fetching again with force refresh
          const refreshedAchievements = await getUserAchievements(true);
          console.log(`[ProfileScreen] Fetched ${refreshedAchievements.length} achievements after cache clear`);

          if (refreshedAchievements.length >= 10) {
            // Use the refreshed achievements
            setAchievements(refreshedAchievements);
          } else {
            // Instead of using default achievements as fallback, show an error or empty state
            console.warn('[ProfileScreen] Backend returned too few achievements, showing empty state.');
            setAchievements([]); // or set an error state if you have one
          }
        } catch (cacheError) {
          console.error('[ProfileScreen] Error clearing cache:', cacheError);
          setAchievements(userAchievements);
        }
      } else {
        // Use the achievements we got
        setAchievements(userAchievements);
      }

      // Get a mix of recent unlocked and in-progress achievements
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      // Get recently unlocked achievements
      const recentUnlocked = userAchievements.filter(a =>
        a.status === AchievementStatus.UNLOCKED &&
        a.unlockedAt &&
        new Date(a.unlockedAt) >= thirtyDaysAgo
      );

      // Sort by unlock date (most recent first)
      recentUnlocked.sort((a, b) => {
        if (!a.unlockedAt || !b.unlockedAt) return 0;
        return new Date(b.unlockedAt).getTime() - new Date(a.unlockedAt).getTime();
      });

      // Get in-progress achievements
      const inProgress = userAchievements.filter(a =>
        a.status === AchievementStatus.IN_PROGRESS
      );

      // Sort by progress (highest progress first)
      inProgress.sort((a, b) => b.progress - a.progress);

      // Combine the two lists, prioritizing unlocked achievements
      // Take up to 2 unlocked and 2 in-progress achievements
      // Create a map to track which achievement IDs we've already added
      const addedAchievementIds = new Set<string>();
      const combinedAchievements: Achievement[] = [];

      // Add unlocked achievements first (up to 2)
      for (let i = 0; i < Math.min(recentUnlocked.length, 2); i++) {
        if (!addedAchievementIds.has(recentUnlocked[i].id)) {
          combinedAchievements.push(recentUnlocked[i]);
          addedAchievementIds.add(recentUnlocked[i].id);
        }
      }

      // Add in-progress achievements (up to 2)
      for (let i = 0; i < Math.min(inProgress.length, 2); i++) {
        if (!addedAchievementIds.has(inProgress[i].id)) {
          combinedAchievements.push(inProgress[i]);
          addedAchievementIds.add(inProgress[i].id);
        }
      }

      // If we don't have 4 achievements yet, add more from either category
      if (combinedAchievements.length < 4) {
        // Add more unlocked achievements if available
        if (recentUnlocked.length > 2) {
          for (let i = 2; i < recentUnlocked.length && combinedAchievements.length < 4; i++) {
            if (!addedAchievementIds.has(recentUnlocked[i].id)) {
              combinedAchievements.push(recentUnlocked[i]);
              addedAchievementIds.add(recentUnlocked[i].id);
            }
          }
        }

        // Add more in-progress achievements if needed
        if (combinedAchievements.length < 4 && inProgress.length > 2) {
          for (let i = 2; i < inProgress.length && combinedAchievements.length < 4; i++) {
            if (!addedAchievementIds.has(inProgress[i].id)) {
              combinedAchievements.push(inProgress[i]);
              addedAchievementIds.add(inProgress[i].id);
            }
          }
        }
      }

      // Set the achievements to display
      setRecentAchievements(combinedAchievements);
    } catch (error) {
      console.error('Error loading profile data:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Handle pull-to-refresh
  const onRefresh = useCallback(async () => {
    setIsRefreshing(true);
    await loadProfileData(true);
    setIsRefreshing(false);
  }, [loadProfileData]);

  // Load data on mount
  useEffect(() => {
    loadProfileData();
  }, [loadProfileData]);

  // Handle scroll animation for header
  const handleScroll = Animated.event(
    [{ nativeEvent: { contentOffset: { y: scrollY } } }],
    { useNativeDriver: false }
  );

  const handleSignOut = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            try {
              await signOut();
              navigation.reset({
                index: 0,
                routes: [{ name: 'SignIn' }],
              });
            } catch (error) {
              console.error('Failed to sign out:', error);
              Alert.alert('Error', 'Failed to sign out. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently deleted.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            // Second confirmation
            Alert.alert(
              'Confirm Deletion',
              'Please confirm that you want to permanently delete your account and all associated data.',
              [
                {
                  text: 'Cancel',
                  style: 'cancel',
                },
                {
                  text: 'Delete Permanently',
                  style: 'destructive',
                  onPress: async () => {
                    try {
                      await deleteAccount();
                      navigation.reset({
                        index: 0,
                        routes: [{ name: 'SignIn' }],
                      });
                    } catch (error) {
                      console.error('Failed to delete account:', error);
                      Alert.alert('Error', 'Failed to delete account. Please try again.');
                    }
                  },
                },
              ]
            );
          },
        },
      ]
    );
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background }}>
      <Animated.View style={{ flex: 1, backgroundColor: animatedColors.background }}>
        <Animated.ScrollView
          style={styles.container}
          onScroll={handleScroll}
          scrollEventThrottle={16}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }
        >
          {/* Header */}
          <Animated.View style={[styles.header, { backgroundColor: animatedColors.background }]}>
            <Animated.Text style={[styles.headerTitle, { color: animatedColors.text }]}>Profile</Animated.Text>
          </Animated.View>

        {/* Profile Section */}
        <Animated.View style={[styles.profileSection, { backgroundColor: animatedColors.background }]}>
          <ProfilePictureUploader
            size={120}
            initialImage={profileImage}
            onImageChange={setProfileImage}
          />

          <Animated.Text style={[styles.profileName, { color: animatedColors.text }]}>
            {profile.name || 'User'}
          </Animated.Text>

          <Animated.Text style={[styles.profileEmail, { color: animatedColors.textSecondary }]}>
            {profile?.userId ? `ID: ${profile.userId.substring(0, 8)}...` : 'No User ID'}
          </Animated.Text>

          <View style={styles.profileButtonsRow}>
            <TouchableOpacity
              style={[styles.editButton, { backgroundColor: colors.primary }]}
              onPress={() => navigation.navigate('EditProfile')}
            >
              <Ionicons name="create-outline" size={16} color="#FFFFFF" />
              <Text style={styles.editButtonText}>Edit Profile</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.achievementsButton, { backgroundColor: colors.card }]}
              onPress={() => setIsAchievementsModalVisible(true)}
            >
              <Ionicons name="trophy-outline" size={16} color={colors.primary} />
              <Text style={[styles.achievementsButtonText, { color: colors.primary }]}>
                Achievements
              </Text>
            </TouchableOpacity>
          </View>
        </Animated.View>

        {/* Progress Summary Section */}
        <Animated.View style={[styles.section, { backgroundColor: animatedColors.background }]}>
          <Animated.Text style={[styles.sectionTitle, { color: animatedColors.text }]}>
            Progress Summary
          </Animated.Text>

          {isLoading ? (
            <ActivityIndicator size="large" color={colors.primary} style={styles.loader} />
          ) : (
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.progressCardsContainer}
            >
              <ProgressSummaryCard
                title="Workouts"
                value={progressData.workouts.count}
                subtitle="This month"
                icon={'fitness' as keyof typeof Ionicons.glyphMap}
                iconColor={colors.primary}
                backgroundColor={colors.card}
                textColor={colors.text}
                subtitleColor={colors.textSecondary}
                trend={progressData.workouts.trend as 'up' | 'neutral' | 'down' | undefined}
                trendValue={progressData.workouts.trendValue}
                onPress={() => navigation.navigate('Progress')}
              />

              <ProgressSummaryCard
                title="Daily Calories"
                value={progressData.calories.value}
                subtitle="Average"
                icon={'restaurant' as keyof typeof Ionicons.glyphMap}
                iconColor="#FF9500"
                backgroundColor={colors.card}
                textColor={colors.text}
                subtitleColor={colors.textSecondary}
                trend={progressData.calories.trend as 'up' | 'neutral' | 'down' | undefined}
                trendValue={progressData.calories.trendValue}
                onPress={() => navigation.navigate('Diet')}
              />

              <ProgressSummaryCard
                title="Current Weight"
                value={`${progressData.weight.value} lbs`}
                subtitle="Last updated today"
                icon={'barbell' as keyof typeof Ionicons.glyphMap}
                iconColor="#5856D6"
                backgroundColor={colors.card}
                textColor={colors.text}
                subtitleColor={colors.textSecondary}
                trend={progressData.weight.trend as 'up' | 'neutral' | 'down' | undefined}
                trendValue={progressData.weight.trendValue}
                onPress={() => navigation.navigate('Progress')}
              />

              <ProgressSummaryCard
                title="Streak"
                value={`${progressData.streak.days} days`}
                subtitle="Keep it up!"
                icon={'flame' as keyof typeof Ionicons.glyphMap}
                iconColor="#FF3B30"
                backgroundColor={colors.card}
                textColor={colors.text}
                subtitleColor={colors.textSecondary}
                trend={progressData.streak.trend as 'up' | 'neutral' | 'down' | undefined}
                trendValue={progressData.streak.trendValue}
              />
            </ScrollView>
          )}
        </Animated.View>

        {/* Achievements Section */}
        <Animated.View style={[styles.section, { backgroundColor: animatedColors.background }]}>
          <View style={styles.sectionHeader}>
            <Animated.Text style={[styles.sectionTitle, { color: animatedColors.text }]}>
              Your Achievements
            </Animated.Text>

            <TouchableOpacity
              onPress={() => setIsAchievementsModalVisible(true)}
              style={styles.viewAllButton}
            >
              <Text style={[styles.viewAllText, { color: colors.primary }]}>
                View All
              </Text>
            </TouchableOpacity>
          </View>

          {isLoading ? (
            <ActivityIndicator size="large" color={colors.primary} style={styles.loader} />
          ) : recentAchievements.length > 0 ? (
            <View style={styles.achievementsContainer}>
              {recentAchievements.map((achievement, index) => (
                <AchievementBadge
                  key={achievement.id}
                  title={achievement.title}
                  description={achievement.description}
                  icon={achievement.icon as keyof typeof Ionicons.glyphMap}
                  unlocked={achievement.status === AchievementStatus.UNLOCKED}
                  progress={achievement.progress}
                  color={colors.primary}
                  backgroundColor={colors.card}
                  textColor={colors.text}
                  delay={index * 100}
                />
              ))}
            </View>
          ) : (
            <View style={styles.emptyAchievements}>
              <Ionicons name="trophy-outline" size={40} color={colors.textTertiary} />
              <Text style={[styles.emptyAchievementsText, { color: colors.textSecondary }]}>
                No achievements to display. Complete activities to earn achievements.
              </Text>
            </View>
          )}
        </Animated.View>

        {/* Settings Section */}
        <Animated.View style={[styles.settingsSection, { backgroundColor: animatedColors.background }]}>
          <Animated.Text style={[styles.sectionTitle, { color: animatedColors.text }]}>
            Settings
          </Animated.Text>

          <AnimatedSettingsItem
            title="Dark Mode"
            description="Toggle dark theme"
            icon={'moon' as keyof typeof Ionicons.glyphMap}
            type="toggle"
            value={isDark}
            onToggle={(value) => setTheme(value ? 'dark' : 'light')}
            iconColor={colors.primary}
            backgroundColor={colors.card}
            textColor={colors.text}
            descriptionColor={colors.textSecondary}
          />

          <AnimatedSettingsItem
            title="Manage Your Context"
            description="View and edit your personal context data"
            icon={'information-circle' as keyof typeof Ionicons.glyphMap}
            type="button"
            onPress={() => setIsContextManagerVisible(true)}
            iconColor={colors.primary}
            backgroundColor={colors.card}
            textColor={colors.text}
            descriptionColor={colors.textSecondary}
          />
















          <AnimatedSettingsItem
            title="🗑️ Delete All Conversations"
            description="Delete all chat conversations (local and server)"
            icon={'chatbubbles-outline' as keyof typeof Ionicons.glyphMap}
            type="navigation"
            onPress={async () => {
              Alert.alert(
                'Delete All Conversations',
                'This will permanently delete ALL your chat conversations from both your device and the server. This action cannot be undone.\n\nYour workouts, meals, and other data will not be affected.',
                [
                  { text: 'Cancel', style: 'cancel' },
                  {
                    text: 'Delete All Conversations',
                    style: 'destructive',
                    onPress: async () => {
                      try {
                        const success = await deleteAllConversations();
                        if (success) {
                          Alert.alert('Success', 'All conversations have been deleted successfully');
                        } else {
                          Alert.alert('Error', 'Failed to delete all conversations. Some may have been deleted.');
                        }
                      } catch (error) {
                        console.error('Error deleting all conversations:', error);
                        Alert.alert('Error', 'An unexpected error occurred while deleting conversations');
                      }
                    },
                  },
                ]
              );
            }}
            iconColor="#FF6B35"
            backgroundColor={colors.card}
            textColor={colors.text}
            descriptionColor={colors.textSecondary}
          />

          <AnimatedSettingsItem
            title="Clear All Cache"
            description="Clear all locally stored data"
            icon={'trash' as keyof typeof Ionicons.glyphMap}
            type="navigation"
            onPress={async () => {
              Alert.alert(
                'Clear All Cache',
                'This will clear ALL locally cached data. Are you sure?',
                [
                  { text: 'Cancel', style: 'cancel' },
                  {
                    text: 'Clear Everything',
                    style: 'destructive',
                    onPress: async () => {
                      try {
                        await clearAllLocalCache();
                        await clearAchievementsCache();
                        await AsyncStorage.removeItem('@achievements_pending_sync');
                        Alert.alert('Success', 'All local cache cleared successfully');
                      } catch (error) {
                        console.error('Error clearing all cache:', error);
                        Alert.alert('Error', 'Failed to clear cache');
                      }
                    },
                  },
                ]
              );
            }}
            iconColor="#FF3B30"
            backgroundColor={colors.card}
            textColor={colors.text}
            descriptionColor={colors.textSecondary}
          />

          <AnimatedSettingsItem
            title="About"
            description="App version and information"
            icon={'information-circle' as keyof typeof Ionicons.glyphMap}
            type="navigation"
            onPress={() => {
              Alert.alert(
                'About',
                'Lotus v2.0.0\n\nTrack your fitness journey with natural language logging.'
              );
            }}
            iconColor="#30B0C7"
            backgroundColor={colors.card}
            textColor={colors.text}
            descriptionColor={colors.textSecondary}
          />
        </Animated.View>

        {/* Account Actions */}
        <Animated.View style={[styles.accountActionsSection, { backgroundColor: animatedColors.background }]}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.card }]}
            onPress={handleSignOut}
          >
            <Ionicons name="log-out" size={20} color={colors.error} />
            <Animated.Text style={[styles.actionButtonText, { color: animatedColors.error }]}>
              Sign Out
            </Animated.Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.card, marginTop: 12 }]}
            onPress={handleDeleteAccount}
          >
            <Ionicons name="trash" size={20} color={colors.error} />
            <Animated.Text style={[styles.actionButtonText, { color: animatedColors.error }]}>
              Delete Account
            </Animated.Text>
          </TouchableOpacity>
        </Animated.View>

        {/* App Version */}
        <Animated.View style={[styles.versionContainer, { backgroundColor: animatedColors.background }]}>
          <Animated.Text style={[styles.versionText, { color: animatedColors.textTertiary }]}>
            Lotus v2.0.0
          </Animated.Text>
        </Animated.View>
        </Animated.ScrollView>

        {/* User Context Manager */}
        <UserContextManager
          visible={isContextManagerVisible}
          onClose={() => setIsContextManagerVisible(false)}
        />

        {/* Achievements Modal */}
        <AchievementsModal
          visible={isAchievementsModalVisible}
          onClose={() => setIsAchievementsModalVisible(false)}
        />
      </Animated.View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    paddingVertical: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.semibold,
  },
  profileSection: {
    alignItems: 'center',
    marginBottom: 32,
  },
  profileName: {
    fontSize: 24,
    fontFamily: typography.fontFamily.bold,
    marginTop: 16,
  },
  profileEmail: {
    fontSize: 16,
    fontFamily: typography.fontFamily.regular,
    marginTop: 4,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginTop: 16,
    gap: 6,
  },
  editButtonText: {
    fontFamily: typography.fontFamily.medium,
    fontSize: typography.sizes.sm,
    color: '#FFFFFF',
  },
  section: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  settingsSection: {
    marginBottom: 32,
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
  },
  settingDescription: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    marginTop: 4,
  },
  accountActionsSection: {
    marginBottom: 32,
    paddingHorizontal: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  actionButtonText: {
    fontFamily: typography.fontFamily.medium,
    fontSize: 16,
  },
  versionContainer: {
    alignItems: 'center',
    marginBottom: Platform.OS === 'ios' ? 90 : 80, // Add extra padding for the tab bar
  },
  versionText: {
    fontSize: 14,
    fontFamily: typography.fontFamily.regular,
  },
  progressCardsContainer: {
    paddingRight: 16,
    paddingBottom: 8,
    gap: 12,
  },
  achievementsContainer: {
    marginTop: 8,
    gap: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  viewAllButton: {
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  viewAllText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
  },
  profileButtonsRow: {
    flexDirection: 'row',
    marginTop: 16,
    gap: 12,
  },
  achievementsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    gap: 6,
  },
  achievementsButtonText: {
    fontFamily: typography.fontFamily.medium,
    fontSize: typography.sizes.sm,
  },
  emptyAchievements: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    backgroundColor: 'transparent',
    borderRadius: 12,
    marginTop: 8,
  },
  emptyAchievementsText: {
    fontFamily: typography.fontFamily.regular,
    fontSize: typography.sizes.sm,
    textAlign: 'center',
    marginTop: 12,
  },
  loader: {
    marginVertical: 20,
  },
});

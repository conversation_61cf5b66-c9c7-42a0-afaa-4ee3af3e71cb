import React from 'react';
import { StyleSheet, View, Animated, Platform } from 'react-native';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../theme/ThemeProvider';
// Now that we've fixed the dependencies, we can use AnimatedThemeProvider
import { AnimatedThemeProvider, useAnimatedTheme } from '../theme/AnimatedThemeProvider';
import StatusBarManager from '../components/StatusBarManager';
import MainTabNavigator from '../navigation/MainTabNavigator';

// This component wraps the AnimatedThemeProvider
export default function MainApp() {
  return (
    <SafeAreaProvider>
      <AnimatedThemeProvider>
        <StatusBarManager />
        <MainAppContent />
      </AnimatedThemeProvider>
    </SafeAreaProvider>
  );
}

// This component uses the animated theme values
function MainAppContent() {
  const { colors } = useTheme();
  const { animatedColors } = useAnimatedTheme();

  return (
    <SafeAreaView
      style={[
        styles.container,
        {
          // Use regular color for initial render
          backgroundColor: colors.background,
          position: 'relative',
          zIndex: 0,
          // Add padding at the bottom to account for the tab bar
          paddingBottom: Platform.OS === 'ios' ? 40 : 35
        }
      ]}
      edges={['right', 'left']} // Remove 'bottom' to avoid double padding with the tab bar
    >
      {/* Animated background wrapper for smooth transitions */}
      <Animated.View
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: animatedColors.background,
          zIndex: 0,
        }}
      />
      <MainTabNavigator />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
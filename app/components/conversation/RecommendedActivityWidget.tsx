import React, { useState, useRef } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions, Animated, Easing } from 'react-native';
import { useTheme } from '@/theme/ThemeProvider';
import { Ionicons } from '@expo/vector-icons';
import { format, parseISO } from 'date-fns';

// Get screen width for responsive sizing
const { width } = Dimensions.get('window');
// Better responsive calculation - accounts for all margins, padding, and spacing
const containerMargins = 32; // marginHorizontal: 16 (16px on each side)
const containerPadding = 32; // padding: 16 (16px on each side)
const cardSpacing = 12; // Space between cards
const cardWidth = (width - containerMargins - containerPadding - cardSpacing) / 2;

interface RecommendedActivityWidgetProps {
  onPressMeal: () => void;
  onPressWorkout: () => void;
  recommendedMeal: any; // Define a proper type later
  recommendedWorkout: any; // Define a proper type later
}

// Helper function to extract muscle groups from workout
const extractMuscleGroups = (workout: any): string => {
  if (!workout) return '';

  // Check if we have specific muscle groups
  if (workout.muscleGroups && workout.muscleGroups.length > 0) {
    return workout.muscleGroups.slice(0, 2).join(', ');
  }

  // Check exercises for muscle groups
  if (workout.exercises && workout.exercises.length > 0) {
    const muscleGroups = new Set<string>();
    
    workout.exercises.forEach((exercise: any) => {
      if (exercise.muscleGroups) {
        exercise.muscleGroups.forEach((group: string) => muscleGroups.add(group));
      }
      if (exercise.targetMuscle) {
        muscleGroups.add(exercise.targetMuscle);
      }
    });

    if (muscleGroups.size > 0) {
      return Array.from(muscleGroups).slice(0, 2).join(', ');
    }
  }

  // Check for workout type or category
  if (workout.type) {
    return workout.type;
  }

  if (workout.category) {
    return workout.category;
  }

  return 'Full Body';
};

// Helper function to extract nutrition info from meal
const extractNutritionInfo = (meal: any): string => {
  if (!meal) return '';

  // Check if we have nutrition data
  if (meal.nutrition) {
    const { calories, protein } = meal.nutrition;
    const parts = [];

    if (calories) parts.push(`${calories} cal`);
    if (protein) parts.push(`${protein}g protein`);

    return parts.join(' • ');
  }

  // Check individual nutrition properties
  const parts = [];
  if (meal.calories) parts.push(`${meal.calories} cal`);
  if (meal.protein) parts.push(`${meal.protein}g protein`);

  return parts.length > 0 ? parts.join(' • ') : 'Nutritional info available';
};

const RecommendedActivityWidget: React.FC<RecommendedActivityWidgetProps> = ({
  onPressMeal,
  onPressWorkout,
  recommendedMeal,
  recommendedWorkout,
}) => {
  const { colors, isDark } = useTheme();
  const [expanded, setExpanded] = useState(true);
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const heightAnim = useRef(new Animated.Value(1)).current;

  // If no recommendations, don't render anything
  if (!recommendedMeal && !recommendedWorkout) {
    return null;
  }

  // Get the actual titles with fallbacks
  const mealTitle = recommendedMeal?.title || "Recommended Meal";
  const workoutTitle = recommendedWorkout?.title || "Recommended Workout";

  // Extract meal ingredients preview (first 2-3 ingredients)
  const ingredientsPreview = recommendedMeal?.ingredients?.length > 0
    ? recommendedMeal.ingredients.slice(0, 2).join(', ') +
      (recommendedMeal.ingredients.length > 2 ? '...' : '')
    : '';

  // Extract workout muscle groups
  const muscleGroups = extractMuscleGroups(recommendedWorkout);

  // Extract meal nutrition info
  const nutritionInfo = extractNutritionInfo(recommendedMeal);

  // Extract exercise count
  const exerciseCount = recommendedWorkout?.exercises?.length || 0;

  // Extract workout duration
  const workoutDuration = recommendedWorkout?.duration
    ? `${recommendedWorkout.duration} min`
    : '';

  // Format scheduled times for display
  const formatScheduledTime = (scheduledTime: string) => {
    try {
      const time = parseISO(scheduledTime);
      return format(time, 'h:mm a');
    } catch (error) {
      return '';
    }
  };

  const mealTime = recommendedMeal?.scheduledTime ? formatScheduledTime(recommendedMeal.scheduledTime) : '';
  const workoutTime = recommendedWorkout?.scheduledTime ? formatScheduledTime(recommendedWorkout.scheduledTime) : '';

  // Toggle expanded state with animation
  const toggleExpanded = () => {
    const toValue = expanded ? 0 : 1;

    // Animate rotation for chevron
    Animated.timing(rotateAnim, {
      toValue,
      duration: 400,
      easing: Easing.bezier(0.4, 0.0, 0.2, 1), // Material Design easing
      useNativeDriver: true
    }).start();

    // Animate height for content with slightly longer duration for smoother feel
    Animated.timing(heightAnim, {
      toValue,
      duration: 400,
      easing: Easing.bezier(0.4, 0.0, 0.2, 1), // Material Design easing
      useNativeDriver: false
    }).start();

    setExpanded(!expanded);
  };

  // Calculate rotation for chevron
  const rotate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg']
  });

  // Calculate height for smooth collapse/expand animation
  const contentHeight = heightAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 195] // 0 when collapsed, 195 when expanded
  });

  // Calculate opacity for content
  const contentOpacity = heightAnim.interpolate({
    inputRange: [0, 0.3, 1],
    outputRange: [0, 0.5, 1]
  });

  // Use scale for additional smooth effect
  const contentScale = heightAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.95, 1]
  });

  // Calculate total container height for smooth animation
  const containerHeight = heightAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [60, 255] // 60px for header only, 255px for header + content
  });

  return (
    <Animated.View style={[
      styles.container,
      {
        backgroundColor: colors.surfaceLight,
        height: containerHeight
      }
    ]}>
      <TouchableOpacity
        style={styles.headerContainer}
        activeOpacity={0.7}
        onPress={toggleExpanded}
      >
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Today's Recommendations
        </Text>
        <Animated.View style={{ transform: [{ rotate }] }}>
          <Ionicons
            name="chevron-down"
            size={20}
            color={colors.textSecondary}
          />
        </Animated.View>
      </TouchableOpacity>

      <Animated.View
        style={[
          styles.animatedContainer,
          {
            height: contentHeight,
            opacity: contentOpacity,
            transform: [{ scaleY: contentScale }]
          }
        ]}
      >
        <View style={styles.cardsContainer}>
        {/* Meal Recommendation */}
        {recommendedMeal && (
          <TouchableOpacity
            onPress={onPressMeal}
            style={[
              styles.recommendationItem,
              {
                backgroundColor: colors.card,
                borderColor: colors.border,
                shadowColor: colors.shadow,
              }
            ]}
            activeOpacity={0.7}
          >
            <View style={styles.cardHeader}>
              <View style={[styles.iconContainer, { backgroundColor: 'rgba(255, 150, 50, 0.15)' }]}>
                <Ionicons name="restaurant-outline" size={18} color="#FF9632" />
              </View>
              
              <View style={styles.headerContent}>
                <Text style={[styles.categoryTitle, { color: colors.textSecondary }]}>Next Meal</Text>
                {mealTime && (
                  <Text style={[styles.timeText, { color: colors.primary }]}>{mealTime}</Text>
                )}
              </View>
            </View>

            <View style={styles.cardContent}>
              <Text style={[styles.recommendationTitle, { color: colors.text }]} numberOfLines={2}>
                {mealTitle}
              </Text>

              {/* Show nutrition info if available */}
              {nutritionInfo && (
                <Text style={[styles.nutritionText, { color: colors.primary }]} numberOfLines={1}>
                  {nutritionInfo}
                </Text>
              )}

              {/* Show ingredients preview if available */}
              {ingredientsPreview && (
                <Text style={[styles.ingredientsText, { color: colors.textSecondary }]} numberOfLines={1}>
                  {ingredientsPreview}
                </Text>
              )}

              {/* Description fallback */}
              {!nutritionInfo && !ingredientsPreview && recommendedMeal.description && (
                <Text style={[styles.descriptionText, { color: colors.textSecondary }]} numberOfLines={2}>
                  {recommendedMeal.description}
                </Text>
              )}
            </View>

            <View style={[styles.viewButton, { borderTopColor: colors.border }]}>
              <Text style={[styles.viewButtonText, { color: colors.primary }]}>View Recipe</Text>
            </View>
          </TouchableOpacity>
        )}

        {/* Workout Recommendation */}
        {recommendedWorkout && (
          <TouchableOpacity
            onPress={onPressWorkout}
            style={[
              styles.recommendationItem,
              {
                backgroundColor: colors.card,
                borderColor: colors.border,
                shadowColor: colors.shadow,
              }
            ]}
            activeOpacity={0.7}
          >
            <View style={styles.cardHeader}>
              <View style={[styles.iconContainer, { backgroundColor: 'rgba(80, 130, 255, 0.15)' }]}>
                <Ionicons name="barbell-outline" size={18} color="#5082FF" />
              </View>
              
              <View style={styles.headerContent}>
                <Text style={[styles.categoryTitle, { color: colors.textSecondary }]}>Next Workout</Text>
                {workoutTime && (
                  <Text style={[styles.timeText, { color: colors.primary }]}>{workoutTime}</Text>
                )}
              </View>
            </View>

            <View style={styles.cardContent}>
              <Text style={[styles.recommendationTitle, { color: colors.text }]} numberOfLines={2}>
                {workoutTitle}
              </Text>

              {/* Show muscle groups if available */}
              {muscleGroups && (
                <Text style={[styles.muscleGroupsText, { color: colors.primary }]} numberOfLines={1}>
                  {muscleGroups}
                </Text>
              )}

              {/* Show exercise count and duration */}
              <View style={styles.workoutMetadata}>
                {exerciseCount > 0 && (
                  <Text style={[styles.metadataText, { color: colors.textSecondary }]}>
                    {exerciseCount} exercise{exerciseCount !== 1 ? 's' : ''}
                  </Text>
                )}
                {workoutDuration && (
                  <Text style={[styles.metadataText, { color: colors.textSecondary }]}>
                    {workoutDuration}
                  </Text>
                )}
              </View>

              {/* Description fallback */}
              {!muscleGroups && recommendedWorkout.description && (
                <Text style={[styles.descriptionText, { color: colors.textSecondary }]} numberOfLines={2}>
                  {recommendedWorkout.description}
                </Text>
              )}
            </View>

            <View style={[styles.viewButton, { borderTopColor: colors.border }]}>
              <Text style={[styles.viewButtonText, { color: colors.primary }]}>View Workout</Text>
            </View>
          </TouchableOpacity>
        )}
        </View>
      </Animated.View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginBottom: 16,
    marginTop: 8,
    borderRadius: 16,
    padding: 16,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden', // Ensure content doesn't overflow during animation
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    letterSpacing: -0.3,
  },
  animatedContainer: {
    overflow: 'hidden',
    // Height is now animated, no fixed height needed
  },
  cardsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start', // Align cards to top
    gap: cardSpacing,
  },
  recommendationItem: {
    width: cardWidth,
    borderRadius: 12,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
    height: 185, // Increased height to accommodate all content
    flexDirection: 'column',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 12,
    paddingBottom: 6,
  },
  iconContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  headerContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryTitle: {
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  timeText: {
    fontSize: 11,
    fontWeight: '500',
  },
  cardContent: {
    flex: 1,
    paddingHorizontal: 12,
    paddingBottom: 4,
    overflow: 'hidden', // Prevent content overflow
    justifyContent: 'flex-start', // Ensure content starts at top
  },
  recommendationTitle: {
    fontSize: 15,
    fontWeight: '600',
    lineHeight: 20,
    marginBottom: 6,
  },
  nutritionText: {
    fontSize: 11,
    fontWeight: '500',
    marginBottom: 4,
    lineHeight: 15,
  },
  ingredientsText: {
    fontSize: 11,
    fontStyle: 'italic',
    lineHeight: 15,
    opacity: 0.8,
  },
  muscleGroupsText: {
    fontSize: 11,
    fontWeight: '500',
    marginBottom: 4,
    lineHeight: 15,
  },
  workoutMetadata: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  metadataText: {
    fontSize: 11,
    lineHeight: 15,
    opacity: 0.8,
  },
  descriptionText: {
    fontSize: 11,
    lineHeight: 15,
    opacity: 0.8,
  },
  viewButton: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderTopWidth: StyleSheet.hairlineWidth,
    alignItems: 'center',
  },
  viewButtonText: {
    fontSize: 12,
    fontWeight: '600',
    letterSpacing: 0.2,
  },
});

export default RecommendedActivityWidget;
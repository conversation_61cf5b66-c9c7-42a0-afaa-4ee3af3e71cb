import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../theme/ThemeProvider';
import { typography } from '../theme/typography';
import { calculateWeightStats, WeightStats as WeightStatsType } from '../services/weightTracking';

interface WeightStatsProps {
  refreshTrigger?: number; // A value that changes to trigger refresh
  compactView?: boolean; // Flag to display in compact format
}

const WeightStats: React.FC<WeightStatsProps> = ({ refreshTrigger, compactView = false }) => {
  const { colors } = useTheme();
  const navigation = useNavigation();
  const [stats, setStats] = useState<WeightStatsType | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadStats();
  }, []);

  // Add a separate effect to reload data when the component receives focus
  useEffect(() => {
    const unsubscribe = navigation?.addListener('focus', () => {
      loadStats();
    });
    return unsubscribe;
  }, [navigation]);

  // Add an effect to reload data when the refreshTrigger changes
  useEffect(() => {
    if (refreshTrigger !== undefined) {
      console.log('WeightStats: Refresh triggered');
      loadStats();
    }
  }, [refreshTrigger]);

  const loadStats = async () => {
    setIsLoading(true);
    try {
      const weightStats = await calculateWeightStats();
      setStats(weightStats);
    } catch (error) {
      console.error('Error loading weight stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getTrendIcon = () => {
    if (!stats || !stats.trend) return null;

    switch (stats.trend) {
      case 'up':
        return <Ionicons name="trending-up" size={20} color={colors.error} />;
      case 'down':
        return <Ionicons name="trending-down" size={20} color={colors.success} />;
      case 'stable':
        return <Ionicons name="remove" size={20} color={colors.textSecondary} />;
      default:
        return null;
    }
  };

  const getChangeColor = () => {
    if (!stats || !stats.change) return colors.textSecondary;

    // For weight, decreasing is usually positive
    return stats.change.isPositive ? colors.error : colors.success;
  };

  if (isLoading) {
    return (
      <View style={[
        compactView ? styles.compactContainer : styles.container, 
        { backgroundColor: 'transparent' }
      ]}>
        <ActivityIndicator size="small" color={colors.primary} />
      </View>
    );
  }

  if (!stats || stats.current === null) {
    return (
      <View style={[
        compactView ? styles.compactContainer : styles.container, 
        { backgroundColor: 'transparent' }
      ]}>
        <Text style={[styles.noDataText, { color: colors.textSecondary }]}>
          No data
        </Text>
      </View>
    );
  }

  // Render compact view if requested
  if (compactView) {
    return (
      <View style={styles.compactContainer}>
        <View style={styles.compactCurrentWeight}>
          <Text style={[styles.compactLabel, { color: colors.textSecondary }]}>
            Current
          </Text>
          <Text style={[styles.compactValue, { color: colors.text }]}>
            {stats.current.toFixed(1)} lbs
          </Text>
        </View>
        
        {stats.change && (
          <View style={styles.compactChange}>
            <View style={styles.changeValueContainer}>
              <Text style={[styles.compactChangeValue, { color: getChangeColor() }]}>
                {stats.change.isPositive ? '↑' : '↓'} {Math.abs(stats.change.percentage).toFixed(1)}%
              </Text>
            </View>
          </View>
        )}
      </View>
    );
  }

  // Render full view
  return (
    <View style={[styles.container, { 
      backgroundColor: colors.card,
      borderRadius: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 1,
    }]}>
      <View style={styles.statRow}>
        <View style={styles.statItem}>
          <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Current</Text>
          <Text style={[styles.statValue, { color: colors.text }]}>
            {stats.current.toFixed(1)} lbs
          </Text>
        </View>

        <View style={styles.statItem}>
          <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Average</Text>
          <Text style={[styles.statValue, { color: colors.text }]}>
            {stats.average ? stats.average.toFixed(1) : '0'} lbs
          </Text>
        </View>
      </View>

      <View style={styles.statRow}>
        <View style={styles.statItem}>
          <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Min</Text>
          <Text style={[styles.statValue, { color: colors.text }]}>
            {stats.min ? stats.min.toFixed(1) : '0'} lbs
          </Text>
        </View>

        <View style={styles.statItem}>
          <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Max</Text>
          <Text style={[styles.statValue, { color: colors.text }]}>
            {stats.max ? stats.max.toFixed(1) : '0'} lbs
          </Text>
        </View>
      </View>

      {stats.change && (
        <View style={styles.changeContainer}>
          <Text style={[styles.changeLabel, { color: colors.textSecondary }]}>
            Overall Change
          </Text>
          <View style={styles.changeValueContainer}>
            <Text style={[styles.changeValue, { color: getChangeColor() }]}>
              {stats.change.isPositive ? '+' : '-'}{stats.change.value.toFixed(1)} lbs
              ({stats.change.percentage.toFixed(1)}%)
            </Text>
            {getTrendIcon()}
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  // Standard view styles
  container: {
    padding: 15,
    borderRadius: 16,
    marginVertical: 10,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  statItem: {
    flex: 1,
  },
  statLabel: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 4,
  },
  statValue: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
  },
  changeContainer: {
    marginTop: 5,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: 'rgba(150, 150, 150, 0.2)',
  },
  changeLabel: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 4,
  },
  changeValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  changeValue: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
    marginRight: 5,
  },
  noDataText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
    textAlign: 'center',
    padding: 20,
  },
  
  // Compact view styles
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingVertical: 0,
  },
  compactCurrentWeight: {
    alignItems: 'flex-end',
    marginRight: 10,
  },
  compactLabel: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.medium,
    letterSpacing: 0.5,
  },
  compactValue: {
    fontSize: typography.sizes.md, 
    fontFamily: typography.fontFamily.semibold,
  },
  compactChange: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  compactChangeValue: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.bold,
  },
});

export default WeightStats;

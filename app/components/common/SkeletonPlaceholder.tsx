import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';
import { useTheme } from '../../theme/ThemeProvider';

interface SkeletonPlaceholderProps {
  width?: number | string;
  height?: number | string;
  borderRadius?: number;
  style?: any;
  children?: React.ReactNode;
}

const SkeletonPlaceholder: React.FC<SkeletonPlaceholderProps> = ({
  width = '100%',
  height = 20,
  borderRadius = 8,
  style,
  children,
}) => {
  const { colors, isDark } = useTheme();
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: false,
        }),
      ])
    );

    animation.start();

    return () => animation.stop();
  }, [animatedValue]);

  const backgroundColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [
      isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.08)',
      isDark ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.15)',
    ],
  });

  if (children) {
    return (
      <View style={[{ width, height, borderRadius }, style]}>
        {children}
      </View>
    );
  }

  return (
    <Animated.View
      style={[
        {
          width,
          height,
          borderRadius,
          backgroundColor,
        },
        style,
      ]}
    />
  );
};

// Predefined skeleton components for common use cases
export const SkeletonCard: React.FC<{ height?: number }> = ({ height = 120 }) => {
  const { colors } = useTheme();
  
  return (
    <View style={[styles.card, { backgroundColor: colors.card, height }]}>
      <View style={styles.cardHeader}>
        <SkeletonPlaceholder width={40} height={40} borderRadius={20} />
        <View style={styles.cardHeaderText}>
          <SkeletonPlaceholder width="60%" height={16} />
          <SkeletonPlaceholder width="40%" height={12} style={{ marginTop: 4 }} />
        </View>
      </View>
      <View style={styles.cardContent}>
        <SkeletonPlaceholder width="100%" height={12} />
        <SkeletonPlaceholder width="80%" height={12} style={{ marginTop: 6 }} />
        <SkeletonPlaceholder width="90%" height={12} style={{ marginTop: 6 }} />
      </View>
    </View>
  );
};

export const SkeletonNutritionRings: React.FC = () => {
  return (
    <View style={styles.ringsContainer}>
      {[1, 2, 3, 4].map((index) => (
        <View key={index} style={styles.ringItem}>
          <SkeletonPlaceholder width={70} height={70} borderRadius={35} />
          <SkeletonPlaceholder width={50} height={12} style={{ marginTop: 8 }} />
          <SkeletonPlaceholder width={40} height={10} style={{ marginTop: 4 }} />
        </View>
      ))}
    </View>
  );
};

export const SkeletonMealSuggestions: React.FC = () => {
  return (
    <View style={styles.suggestionsContainer}>
      {[1, 2, 3].map((index) => (
        <View key={index} style={styles.suggestionItem}>
          <SkeletonPlaceholder width={80} height={80} borderRadius={12} />
          <View style={styles.suggestionText}>
            <SkeletonPlaceholder width="100%" height={14} />
            <SkeletonPlaceholder width="70%" height={12} style={{ marginTop: 4 }} />
            <SkeletonPlaceholder width="50%" height={10} style={{ marginTop: 4 }} />
          </View>
        </View>
      ))}
    </View>
  );
};

export const SkeletonRecommendedActivity: React.FC = () => {
  const { colors } = useTheme();
  
  return (
    <View style={[styles.recommendedContainer, { backgroundColor: colors.surfaceLight }]}>
      <View style={styles.recommendedHeader}>
        <SkeletonPlaceholder width="60%" height={18} />
        <SkeletonPlaceholder width={20} height={20} borderRadius={10} />
      </View>
      <View style={styles.recommendedCards}>
        <View style={[styles.recommendedCard, { backgroundColor: colors.card }]}>
          <View style={styles.cardHeader}>
            <SkeletonPlaceholder width={28} height={28} borderRadius={14} />
            <View style={styles.cardHeaderText}>
              <SkeletonPlaceholder width="60%" height={12} />
              <SkeletonPlaceholder width="40%" height={10} style={{ marginTop: 2 }} />
            </View>
          </View>
          <View style={styles.cardContent}>
            <SkeletonPlaceholder width="100%" height={15} />
            <SkeletonPlaceholder width="80%" height={11} style={{ marginTop: 6 }} />
            <SkeletonPlaceholder width="60%" height={11} style={{ marginTop: 4 }} />
          </View>
          <View style={styles.cardFooter}>
            <SkeletonPlaceholder width="50%" height={12} />
          </View>
        </View>
        <View style={[styles.recommendedCard, { backgroundColor: colors.card }]}>
          <View style={styles.cardHeader}>
            <SkeletonPlaceholder width={28} height={28} borderRadius={14} />
            <View style={styles.cardHeaderText}>
              <SkeletonPlaceholder width="60%" height={12} />
              <SkeletonPlaceholder width="40%" height={10} style={{ marginTop: 2 }} />
            </View>
          </View>
          <View style={styles.cardContent}>
            <SkeletonPlaceholder width="100%" height={15} />
            <SkeletonPlaceholder width="80%" height={11} style={{ marginTop: 6 }} />
            <SkeletonPlaceholder width="60%" height={11} style={{ marginTop: 4 }} />
          </View>
          <View style={styles.cardFooter}>
            <SkeletonPlaceholder width="50%" height={12} />
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 15,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  cardHeaderText: {
    marginLeft: 12,
    flex: 1,
  },
  cardContent: {
    flex: 1,
  },
  ringsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 16,
  },
  ringItem: {
    alignItems: 'center',
  },
  suggestionsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    gap: 12,
  },
  suggestionItem: {
    alignItems: 'center',
    width: 100,
  },
  suggestionText: {
    marginTop: 8,
    width: '100%',
  },
  recommendedContainer: {
    marginHorizontal: 16,
    marginBottom: 16,
    marginTop: 8,
    borderRadius: 16,
    padding: 16,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  recommendedHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  recommendedCards: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  recommendedCard: {
    flex: 1,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
    height: 185,
  },
  cardFooter: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: 'rgba(0,0,0,0.1)',
    alignItems: 'center',
  },
});

export default SkeletonPlaceholder;

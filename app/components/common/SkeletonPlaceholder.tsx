import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';
import { useTheme } from '../../theme/ThemeProvider';

interface SkeletonPlaceholderProps {
  width?: number | string;
  height?: number | string;
  borderRadius?: number;
  style?: any;
  children?: React.ReactNode;
}

const SkeletonPlaceholder: React.FC<SkeletonPlaceholderProps> = ({
  width = '100%',
  height = 20,
  borderRadius = 8,
  style,
  children,
}) => {
  const { colors, isDark } = useTheme();
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: false,
        }),
      ])
    );

    animation.start();

    return () => animation.stop();
  }, [animatedValue]);

  const backgroundColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [
      isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.08)',
      isDark ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.15)',
    ],
  });

  if (children) {
    return (
      <View style={[{ width, height, borderRadius }, style]}>
        {children}
      </View>
    );
  }

  return (
    <Animated.View
      style={[
        {
          width,
          height,
          borderRadius,
          backgroundColor,
        },
        style,
      ]}
    />
  );
};

// Predefined skeleton components for common use cases
export const SkeletonCard: React.FC<{ height?: number }> = ({ height = 120 }) => {
  const { colors } = useTheme();
  
  return (
    <View style={[styles.card, { backgroundColor: colors.card, height }]}>
      <View style={styles.cardHeader}>
        <SkeletonPlaceholder width={40} height={40} borderRadius={20} />
        <View style={styles.cardHeaderText}>
          <SkeletonPlaceholder width="60%" height={16} />
          <SkeletonPlaceholder width="40%" height={12} style={{ marginTop: 4 }} />
        </View>
      </View>
      <View style={styles.cardContent}>
        <SkeletonPlaceholder width="100%" height={12} />
        <SkeletonPlaceholder width="80%" height={12} style={{ marginTop: 6 }} />
        <SkeletonPlaceholder width="90%" height={12} style={{ marginTop: 6 }} />
      </View>
    </View>
  );
};

export const SkeletonNutritionRings: React.FC = () => {
  return (
    <View style={styles.ringsContainer}>
      {[1, 2, 3, 4].map((index) => (
        <View key={index} style={styles.ringItem}>
          <SkeletonPlaceholder width={70} height={70} borderRadius={35} />
          <SkeletonPlaceholder width={50} height={12} style={{ marginTop: 8 }} />
          <SkeletonPlaceholder width={40} height={10} style={{ marginTop: 4 }} />
        </View>
      ))}
    </View>
  );
};

export const SkeletonMealSuggestions: React.FC = () => {
  return (
    <View style={styles.suggestionsContainer}>
      {[1, 2, 3].map((index) => (
        <View key={index} style={styles.suggestionItem}>
          <SkeletonPlaceholder width={80} height={80} borderRadius={12} />
          <View style={styles.suggestionText}>
            <SkeletonPlaceholder width="100%" height={14} />
            <SkeletonPlaceholder width="70%" height={12} style={{ marginTop: 4 }} />
            <SkeletonPlaceholder width="50%" height={10} style={{ marginTop: 4 }} />
          </View>
        </View>
      ))}
    </View>
  );
};

export const SkeletonMealHistory: React.FC = () => {
  const { colors } = useTheme();

  return (
    <View style={styles.mealHistoryContainer}>
      <View style={styles.mealHistoryHeader}>
        <View style={styles.cardHeader}>
          <SkeletonPlaceholder width={20} height={20} borderRadius={10} />
          <SkeletonPlaceholder width="40%" height={18} style={{ marginLeft: 8 }} />
        </View>
        <SkeletonPlaceholder width="20%" height={14} />
      </View>

      {[1, 2, 3].map((index) => (
        <View key={index} style={[styles.mealHistoryItem, { backgroundColor: colors.card }]}>
          <View style={styles.mealHistoryItemHeader}>
            <View style={styles.mealHistoryItemLeft}>
              <SkeletonPlaceholder width="60%" height={16} />
              <SkeletonPlaceholder width="40%" height={12} style={{ marginTop: 4 }} />
            </View>
            <View style={styles.mealHistoryItemRight}>
              <SkeletonPlaceholder width={60} height={12} />
              <SkeletonPlaceholder width={20} height={20} borderRadius={10} style={{ marginLeft: 5 }} />
            </View>
          </View>
        </View>
      ))}
    </View>
  );
};

export const SkeletonWeightStats: React.FC<{ compactView?: boolean }> = ({ compactView = false }) => {
  const { colors } = useTheme();

  if (compactView) {
    return (
      <View style={styles.compactStatsContainer}>
        <View style={styles.compactStatsItem}>
          <SkeletonPlaceholder width={40} height={10} />
          <SkeletonPlaceholder width={60} height={16} style={{ marginTop: 2 }} />
        </View>
        <View style={styles.compactStatsItem}>
          <SkeletonPlaceholder width={30} height={14} />
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.weightStatsContainer, { backgroundColor: colors.card }]}>
      <View style={styles.weightStatsRow}>
        <View style={styles.weightStatsItem}>
          <SkeletonPlaceholder width="50%" height={12} />
          <SkeletonPlaceholder width="70%" height={18} style={{ marginTop: 4 }} />
        </View>
        <View style={styles.weightStatsItem}>
          <SkeletonPlaceholder width="50%" height={12} />
          <SkeletonPlaceholder width="70%" height={18} style={{ marginTop: 4 }} />
        </View>
      </View>
      <View style={styles.weightStatsRow}>
        <View style={styles.weightStatsItem}>
          <SkeletonPlaceholder width="30%" height={12} />
          <SkeletonPlaceholder width="60%" height={18} style={{ marginTop: 4 }} />
        </View>
        <View style={styles.weightStatsItem}>
          <SkeletonPlaceholder width="30%" height={12} />
          <SkeletonPlaceholder width="60%" height={18} style={{ marginTop: 4 }} />
        </View>
      </View>
      <View style={styles.weightStatsChange}>
        <SkeletonPlaceholder width="40%" height={12} />
        <View style={styles.weightStatsChangeValue}>
          <SkeletonPlaceholder width="60%" height={16} />
          <SkeletonPlaceholder width={20} height={20} borderRadius={10} style={{ marginLeft: 5 }} />
        </View>
      </View>
    </View>
  );
};

export const SkeletonDailyDigest: React.FC = () => {
  const { colors } = useTheme();

  return (
    <View style={styles.digestContainer}>
      {[1, 2, 3, 4, 5].map((index) => (
        <View key={index} style={[styles.digestItem, { backgroundColor: colors.card }]}>
          <View style={styles.digestItemLeft}>
            <SkeletonPlaceholder width={40} height={40} borderRadius={20} />
          </View>
          <View style={styles.digestItemContent}>
            <View style={styles.digestItemHeader}>
              <SkeletonPlaceholder width="60%" height={16} />
              <SkeletonPlaceholder width="30%" height={12} />
            </View>
            <SkeletonPlaceholder width="100%" height={14} style={{ marginTop: 8 }} />
            <SkeletonPlaceholder width="80%" height={12} style={{ marginTop: 4 }} />
          </View>
          <View style={styles.digestItemRight}>
            <SkeletonPlaceholder width={20} height={20} borderRadius={10} />
          </View>
        </View>
      ))}
    </View>
  );
};

export const SkeletonRecommendedActivity: React.FC = () => {
  const { colors } = useTheme();

  return (
    <View style={[styles.recommendedContainer, { backgroundColor: colors.surfaceLight }]}>
      <View style={styles.recommendedHeader}>
        <SkeletonPlaceholder width="60%" height={18} />
        <SkeletonPlaceholder width={20} height={20} borderRadius={10} />
      </View>
      <View style={styles.recommendedContent}>
        <View style={styles.recommendedCards}>
          <View style={[styles.recommendedCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
            <View style={styles.skeletonCardHeader}>
              <SkeletonPlaceholder width={28} height={28} borderRadius={14} />
              <View style={styles.skeletonHeaderContent}>
                <SkeletonPlaceholder width="60%" height={12} />
                <SkeletonPlaceholder width="40%" height={10} style={{ marginTop: 2 }} />
              </View>
            </View>
            <View style={styles.skeletonCardContent}>
              <SkeletonPlaceholder width="100%" height={15} />
              <SkeletonPlaceholder width="80%" height={11} style={{ marginTop: 6 }} />
              <SkeletonPlaceholder width="60%" height={11} style={{ marginTop: 4 }} />
            </View>
            <View style={styles.skeletonCardFooter}>
              <SkeletonPlaceholder width="50%" height={12} />
            </View>
          </View>
          <View style={[styles.recommendedCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
            <View style={styles.skeletonCardHeader}>
              <SkeletonPlaceholder width={28} height={28} borderRadius={14} />
              <View style={styles.skeletonHeaderContent}>
                <SkeletonPlaceholder width="60%" height={12} />
                <SkeletonPlaceholder width="40%" height={10} style={{ marginTop: 2 }} />
              </View>
            </View>
            <View style={styles.skeletonCardContent}>
              <SkeletonPlaceholder width="100%" height={15} />
              <SkeletonPlaceholder width="80%" height={11} style={{ marginTop: 6 }} />
              <SkeletonPlaceholder width="60%" height={11} style={{ marginTop: 4 }} />
            </View>
            <View style={styles.skeletonCardFooter}>
              <SkeletonPlaceholder width="50%" height={12} />
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 15,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  cardHeaderText: {
    marginLeft: 12,
    flex: 1,
  },
  cardContent: {
    flex: 1,
  },
  ringsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 16,
  },
  ringItem: {
    alignItems: 'center',
  },
  suggestionsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    gap: 12,
  },
  suggestionItem: {
    alignItems: 'center',
    width: 100,
  },
  suggestionText: {
    marginTop: 8,
    width: '100%',
  },
  recommendedContainer: {
    marginHorizontal: 16,
    marginBottom: 16,
    marginTop: 8,
    borderRadius: 16,
    padding: 16,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  recommendedHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  recommendedContent: {
    height: 195, // Fixed height to match real widget
    overflow: 'hidden',
  },
  recommendedCards: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  recommendedCard: {
    flex: 1,
    borderRadius: 12,
    borderWidth: 1,
    height: 185,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  skeletonCardHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 12,
    paddingBottom: 6,
  },
  skeletonHeaderContent: {
    flex: 1,
    marginLeft: 8,
  },
  skeletonCardContent: {
    flex: 1,
    paddingHorizontal: 12,
    paddingBottom: 4,
    justifyContent: 'flex-start',
  },
  skeletonCardFooter: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: 'rgba(0,0,0,0.1)',
    alignItems: 'center',
  },
  cardFooter: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: 'rgba(0,0,0,0.1)',
    alignItems: 'center',
  },
  mealHistoryContainer: {
    marginBottom: 20,
  },
  mealHistoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 15,
    paddingHorizontal: 5,
  },
  mealHistoryItem: {
    borderRadius: 12,
    marginBottom: 12,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    height: 70, // Fixed height to prevent layout shifts
  },
  mealHistoryItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    height: 70, // Match the container height
  },
  mealHistoryItemLeft: {
    flex: 1,
  },
  mealHistoryItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  compactStatsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingVertical: 0,
    height: 40, // Fixed height
  },
  compactStatsItem: {
    alignItems: 'flex-end',
    marginRight: 10,
  },
  weightStatsContainer: {
    padding: 15,
    borderRadius: 16,
    marginVertical: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
    height: 160, // Fixed height to prevent layout shifts
  },
  weightStatsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  weightStatsItem: {
    flex: 1,
  },
  weightStatsChange: {
    marginTop: 5,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: 'rgba(150, 150, 150, 0.2)',
  },
  weightStatsChangeValue: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  digestContainer: {
    paddingVertical: 10,
  },
  digestItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    height: 80, // Fixed height to prevent layout shifts
  },
  digestItemLeft: {
    marginRight: 12,
  },
  digestItemContent: {
    flex: 1,
  },
  digestItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  digestItemRight: {
    marginLeft: 12,
  },
});

export default SkeletonPlaceholder;

import React, { useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring, 
  withTiming,
  withSequence,
  FadeIn,
  FadeOut,
  SlideInRight,
  Easing
} from 'react-native-reanimated';
import { useTheme } from '../../theme/ThemeProvider';
import { typography } from '../../theme/typography';
import { useNavigation } from '@react-navigation/native';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import type { MainTabParamList } from '../../navigation/EnhancedTabNavigator';
import TimeDisplay from '../TimeDisplay';
import MiniWeatherDisplay from '../MiniWeatherDisplay';
import { useWeather } from '../../context/WeatherContext';

interface HeaderProps {
  title: string;
}

const Header: React.FC<HeaderProps> = ({ title }) => {
  const { colors } = useTheme();
  const navigation = useNavigation<BottomTabNavigationProp<MainTabParamList>>();
  const { weatherData, isLoading: loadingWeather, refreshWeather } = useWeather();
  
  // Animation values
  const titleOpacity = useSharedValue(0);
  const titleTranslateY = useSharedValue(10);
  const iconScale = useSharedValue(0.9);

  // Initialize animations
  useEffect(() => {
    // Title animation with slight delay
    setTimeout(() => {
      titleOpacity.value = withTiming(1, {
        duration: 350,
        easing: Easing.out(Easing.cubic)
      });
      titleTranslateY.value = withTiming(0, {
        duration: 400,
        easing: Easing.out(Easing.cubic)
      });
    }, 100);
    
    // Icon animation
    iconScale.value = withSpring(1, {
      damping: 15,
      stiffness: 120,
      mass: 0.8
    });
  }, []);

  // Animated styles
  const animatedTitleStyle = useAnimatedStyle(() => {
    return {
      opacity: titleOpacity.value,
      transform: [{ translateY: titleTranslateY.value }]
    };
  });

  const animatedIconStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: iconScale.value }]
    };
  });

  // Button press effect
  const handleProfilePress = () => {
    // Quick scale animation for button press feedback
    iconScale.value = withSequence(
      withTiming(0.9, { duration: 100 }),
      withTiming(1, { duration: 150 })
    );
    
    navigation.navigate('Profile');
  };

  return (
    <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.border }]}>
      <View style={styles.headerLeft}>
        <Animated.Text 
          style={[styles.headerTitle, { color: colors.text }, animatedTitleStyle]}
        >
          {title}
        </Animated.Text>
      </View>
      
      <Animated.View 
        style={[styles.headerRight]}
        entering={SlideInRight.duration(400).delay(150)}
      >
        <Animated.View style={{ marginRight: 12 }}>
          <TimeDisplay size="small" showIcon={true} showDate={false} />
        </Animated.View>
        
        <Animated.View style={{ marginRight: 8 }}>
          <MiniWeatherDisplay
            weatherData={weatherData ?? null}
            isLoading={loadingWeather ?? false}
            onRefresh={() => refreshWeather(true)}
          />
        </Animated.View>
        
        <Animated.View style={animatedIconStyle}>
          <TouchableOpacity
            onPress={handleProfilePress}
            style={styles.headerButton}
            activeOpacity={0.7}
          >
            <Ionicons name="person-circle-outline" size={28} color={colors.primary} />
          </TouchableOpacity>
        </Animated.View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    paddingHorizontal: 16,
    paddingVertical: Platform.OS === 'android' ? 12 : 8,
    height: 60,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.semibold,
  },
  headerButton: {
    padding: 6,
    marginLeft: 8,
  },
});

export default Header;

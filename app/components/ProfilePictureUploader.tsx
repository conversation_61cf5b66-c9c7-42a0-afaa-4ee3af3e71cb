import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Platform,
  Alert,
  ActivityIndicator,
  Animated,
} from 'react-native';
// Now that we've installed expo-image-picker, we can use it
import * as ImagePicker from 'expo-image-picker';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../theme/typography';
import { useTheme } from '../theme/ThemeProvider';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface ProfilePictureUploaderProps {
  size?: number;
  onImageChange?: (uri: string | null) => void;
  initialImage?: string | null;
}

const ProfilePictureUploader: React.FC<ProfilePictureUploaderProps> = ({
  size = 120,
  onImageChange,
  initialImage = null,
}) => {
  const { colors } = useTheme();
  const [image, setImage] = useState<string | null>(initialImage);
  const [isLoading, setIsLoading] = useState(false);
  const [pulseAnim] = useState(new Animated.Value(1));

  // Start pulsing animation when hovering
  const startPulse = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.05,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  // Stop pulsing animation
  const stopPulse = () => {
    pulseAnim.stopAnimation();
    Animated.timing(pulseAnim, {
      toValue: 1,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  const pickImage = async () => {
    try {
      setIsLoading(true);

      // Request permission
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please allow access to your photo library to upload a profile picture.');
        setIsLoading(false);
        return;
      }

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const selectedImage = result.assets[0].uri;
        setImage(selectedImage);

        // Save to AsyncStorage
        await AsyncStorage.setItem('profilePicture', selectedImage);

        // Notify parent component
        if (onImageChange) {
          onImageChange(selectedImage);
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const removeImage = async () => {
    if (!image) return;

    Alert.alert(
      'Remove Profile Picture',
      'Are you sure you want to remove your profile picture?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            setImage(null);
            await AsyncStorage.removeItem('profilePicture');
            if (onImageChange) {
              onImageChange(null);
            }
          },
        },
      ]
    );
  };

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.imageContainer,
          {
            width: size,
            height: size,
            borderRadius: size / 2,
            backgroundColor: colors.primaryLight + '40',
            transform: [{ scale: pulseAnim }],
          },
        ]}
      >
        {isLoading ? (
          <ActivityIndicator size="large" color={colors.primary} />
        ) : image ? (
          <>
            <Image
              source={{ uri: image }}
              style={[
                styles.image,
                {
                  width: size,
                  height: size,
                  borderRadius: size / 2,
                },
              ]}
            />
            <TouchableOpacity
              style={[
                styles.editButton,
                { backgroundColor: colors.primary },
              ]}
              onPress={pickImage}
              onPressIn={startPulse}
              onPressOut={stopPulse}
            >
              <Ionicons name="camera" size={18} color="#fff" />
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.removeButton,
                { backgroundColor: colors.error },
              ]}
              onPress={removeImage}
            >
              <Ionicons name="close" size={14} color="#fff" />
            </TouchableOpacity>
          </>
        ) : (
          <TouchableOpacity
            style={styles.uploadButton}
            onPress={pickImage}
            onPressIn={startPulse}
            onPressOut={stopPulse}
          >
            <Ionicons name="person" size={size / 2} color={colors.primary} />
            <View
              style={[
                styles.addIconContainer,
                { backgroundColor: colors.primary },
              ]}
            >
              <Ionicons name="add" size={16} color="#fff" />
            </View>
          </TouchableOpacity>
        )}
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  image: {
    resizeMode: 'cover',
  },
  uploadButton: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addIconContainer: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  editButton: {
    position: 'absolute',
    bottom: 5,
    right: 5,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  removeButton: {
    position: 'absolute',
    top: 5,
    right: 5,
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1.5,
    borderColor: '#fff',
  },
});

export default ProfilePictureUploader;

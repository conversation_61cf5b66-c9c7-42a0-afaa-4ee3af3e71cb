import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Modal,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  Platform
} from 'react-native';
import { useTheme } from '../theme/ThemeProvider';
import { useUserContext } from '../context/UserContextProvider';
import { ContextType } from '../services/contextEngineV3';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../theme/typography';

interface UserContextManagerProps {
  visible: boolean;
  onClose: () => void;
}

const UserContextManager: React.FC<UserContextManagerProps> = ({ visible, onClose }) => {
  const { colors } = useTheme();
  const {
    contextData,
    isLoading,
    addContextData,
    deleteContextItem,
    refreshContextData
  } = useUserContext();

  const [activeTab, setActiveTab] = useState<ContextType>(ContextType.DIETARY_RESTRICTIONS);
  const [newItemText, setNewItemText] = useState('');
  const [isAdding, setIsAdding] = useState(false);
  const [overwriteMode, setOverwriteMode] = useState(false);

  // Filter context data by type
  const filteredData = contextData.filter(item => item.contextType === activeTab);

  // Handle adding a new context item
  const handleAddItem = async () => {
    if (!newItemText.trim()) {
      return;
    }

    setIsAdding(true);

    try {
      let success = false;
      
      // Special handling for preferences in overwrite mode
      if (overwriteMode && activeTab === ContextType.PREFERENCE) {
        // For preferences, we can use the specialized overridePreferences function
        success = await overridePreferences(newItemText.trim());
        if (success) {
          Alert.alert(
            "Preferences Overwritten", 
            "All previous preferences have been replaced with this new preference.",
            [
              { text: "OK", style: "default" }
            ]
          );
        }
      } else {
        // Normal add operation for other types
        success = await addContextData({
          contextType: activeTab,
          value: newItemText.trim(),
          source: 'user_input'
        });
      }

      if (success) {
        setNewItemText('');
      } else {
        Alert.alert('Error', 'Failed to add item. Please try again.');
      }
    } catch (error) {
      console.error('Error adding context item:', error);
      Alert.alert('Error', 'Failed to add item. Please try again.');
    } finally {
      setIsAdding(false);
    }
  };

  // Handle deleting a context item
  const handleDeleteItem = async (key: string) => {
    try {
      await deleteContextItem(key);
    } catch (error) {
      console.error('Error deleting context item:', error);
      Alert.alert('Error', 'Failed to delete item. Please try again.');
    }
  };

  // Render a tab button
  const renderTabButton = (type: ContextType, label: string) => (
    <TouchableOpacity
      style={[
        styles.tabButton,
        activeTab === type && { backgroundColor: colors.primary + '20' }
      ]}
      onPress={() => setActiveTab(type)}
    >
      <Text
        style={[
          styles.tabButtonText,
          { color: activeTab === type ? colors.primary : colors.textSecondary },
          activeTab === type && { fontFamily: typography.fontFamily.semibold }
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  // Get a human-readable label for a context type
  const getContextTypeLabel = (type: ContextType): string => {
    switch (type) {
      case ContextType.DIETARY_RESTRICTION:
        return 'Dietary Restrictions';
      case ContextType.INJURY:
        return 'Injuries';
      case ContextType.LIFE_UPDATE:
        return 'Life Updates';
      case ContextType.PREFERENCE:
        return 'Preferences';
      case ContextType.GOAL:
        return 'Goals';
      case ContextType.WORKOUT_HISTORY:
        return 'Workout History';
      case ContextType.MEAL_HISTORY:
        return 'Meal History';
      case ContextType.WEIGHT_HISTORY:
        return 'Weight History';
      case ContextType.CHAT_SUMMARY:
        return 'Chat Summaries';
      default:
        return 'Custom';
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.background }]}>
        <View style={[styles.header, { borderBottomColor: colors.border }]}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Ionicons name="close" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Manage Your Context
          </Text>
          <TouchableOpacity style={styles.refreshButton} onPress={refreshContextData}>
            <Ionicons name="refresh" size={24} color={colors.primary} />
          </TouchableOpacity>
        </View>

        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tabsContainer}>
          {renderTabButton(ContextType.DIETARY_RESTRICTION, 'Diet')}
          {renderTabButton(ContextType.INJURY, 'Injuries')}
          {renderTabButton(ContextType.GOAL, 'Goals')}
          {renderTabButton(ContextType.PREFERENCE, 'Preferences')}
          {renderTabButton(ContextType.LIFE_UPDATE, 'Life Updates')}
        </ScrollView>

        <View style={styles.contentContainer}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {getContextTypeLabel(activeTab)}
          </Text>

          {isLoading ? (
            <ActivityIndicator size="large" color={colors.primary} style={styles.loader} />
          ) : (
            <>
              <ScrollView style={styles.itemsContainer}>
                {filteredData.length === 0 ? (
                  <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
                    No items found. Add some below.
                  </Text>
                ) : (
                  filteredData.map((item, index) => (
                    <View
                      key={`${item.contextType}#${item.timestamp || index}`}
                      style={[styles.itemContainer, { backgroundColor: colors.card }]}
                    >
                      <View style={{ flex: 1 }}>
                        <Text style={[styles.itemText, { color: colors.text }]}>
                          {item.value}
                        </Text>
                        {item.metadata && (
                          <View style={styles.metadataContainer}>
                            {item.source && (
                              <Text style={[styles.metadataText, { color: colors.textSecondary }]}>
                                Source: {item.source}
                              </Text>
                            )}
                            {item.metadata.confidence && (
                              <Text style={[styles.metadataText, { color: colors.textSecondary }]}>
                                Confidence: {item.metadata.confidence.toFixed(2)}
                              </Text>
                            )}
                            {item.metadata.sentiment && (
                              <Text style={[styles.metadataText, { color: colors.textSecondary }]}>
                                Sentiment: {item.metadata.sentiment}
                              </Text>
                            )}
                          </View>
                        )}
                      </View>
                      <TouchableOpacity
                        style={styles.deleteButton}
                        onPress={() => handleDeleteItem(`${item.contextType}#${item.timestamp}`)}
                      >
                        <Ionicons name="trash-outline" size={20} color={colors.error} />
                      </TouchableOpacity>
                    </View>
                  ))
                )}
              </ScrollView>

              <View style={styles.inputSection}>
                {activeTab === ContextType.PREFERENCE && (
                  <TouchableOpacity 
                    style={[
                      styles.overwriteToggle, 
                      { 
                        backgroundColor: overwriteMode ? colors.primary + '20' : colors.card,
                        borderColor: overwriteMode ? colors.primary : colors.border 
                      }
                    ]}
                    onPress={() => setOverwriteMode(!overwriteMode)}
                  >
                    <Ionicons 
                      name={overwriteMode ? "checkbox" : "square-outline"} 
                      size={20} 
                      color={overwriteMode ? colors.primary : colors.text} 
                    />
                    <Text style={[
                      styles.overwriteText, 
                      { color: overwriteMode ? colors.primary : colors.text }
                    ]}>
                      Overwrite All Preferences
                    </Text>
                    <TouchableOpacity 
                      style={styles.infoButton}
                      onPress={() => Alert.alert(
                        "Overwrite Mode", 
                        "When enabled, this will replace ALL existing preferences with the new preference. Use this option when you want Lotus to forget previous preferences and only consider this new one.",
                        [{ text: "Got it", style: "default" }]
                      )}
                    >
                      <Ionicons name="information-circle" size={18} color={colors.primary} />
                    </TouchableOpacity>
                  </TouchableOpacity>
                )}
                
                <View style={[styles.addContainer, { borderTopColor: colors.border }]}>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        backgroundColor: colors.card,
                        color: colors.text,
                        borderColor: colors.border
                      }
                    ]}
                    placeholder={`${overwriteMode && activeTab === ContextType.PREFERENCE ? 'Replace all with' : 'Add a'} new ${getContextTypeLabel(activeTab).toLowerCase().slice(0, -1)}...`}
                    placeholderTextColor={colors.textSecondary}
                    value={newItemText}
                    onChangeText={setNewItemText}
                    onSubmitEditing={handleAddItem}
                  />
                  <TouchableOpacity
                    style={[
                      styles.addButton,
                      { backgroundColor: overwriteMode && activeTab === ContextType.PREFERENCE ? colors.warning : colors.primary },
                      isAdding && { opacity: 0.7 }
                    ]}
                    onPress={handleAddItem}
                    disabled={isAdding || !newItemText.trim()}
                  >
                    {isAdding ? (
                      <ActivityIndicator size="small" color="#fff" />
                    ) : (
                      <Ionicons name={overwriteMode && activeTab === ContextType.PREFERENCE ? "refresh" : "add"} size={24} color="#fff" />
                    )}
                  </TouchableOpacity>
                </View>
              </View>
            </>
          )}
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  metadataContainer: {
    marginTop: 8,
  },
  metadataText: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
  },
  inputSection: {
    marginTop: 16,
  },
  overwriteToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 8,
    marginBottom: 12,
    borderWidth: 1,
  },
  overwriteText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    marginLeft: 8,
    flex: 1,
  },
  infoButton: {
    padding: 4,
  },
  modalContainer: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
  },
  closeButton: {
    padding: 8,
  },
  refreshButton: {
    padding: 8,
  },
  tabsContainer: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 8,
  },
  tabButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 20,
  },
  tabButtonText: {
    fontSize: typography.sizes.md,
  },
  contentContainer: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 16,
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemsContainer: {
    flex: 1,
  },
  emptyText: {
    textAlign: 'center',
    marginTop: 32,
    fontSize: typography.sizes.md,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  itemText: {
    flex: 1,
    fontSize: typography.sizes.md,
  },
  deleteButton: {
    padding: 8,
  },
  addContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 16,
    borderTopWidth: 1,
    marginTop: 16,
  },
  input: {
    flex: 1,
    height: 48,
    borderRadius: 24,
    paddingHorizontal: 16,
    marginRight: 12,
    borderWidth: 1,
  },
  addButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default UserContextManager;

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  ScrollView,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../../theme/typography';
import { useTheme } from '../../theme/ThemeProvider';
import Animated, { FadeIn, FadeOut, SlideInDown, SlideOutDown } from 'react-native-reanimated';
import { saveContextData, ContextType } from '../../services/contextService';
import { format } from 'date-fns';
import { saveDigestToAPI, generateBasicActivities } from '../../services/digestService';
import { getApiUrl } from '../../services/apiClient';
import { getAuthHeader } from '../../services/auth';

interface DigestContextModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  date: Date;
  resetFuturePlans?: boolean;
}

const DigestContextModal: React.FC<DigestContextModalProps> = ({
  visible,
  onClose,
  onSuccess,
  date,
  resetFuturePlans = false
}) => {
  const { colors, isDark } = useTheme();
  const [contextInput, setContextInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async () => {
    if (!contextInput.trim()) {
      setError('Please enter your preferences or context');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log('[DigestContextModal] Starting context save process');
      console.log('[DigestContextModal] Input text:', contextInput);
      
      // Generate a unique message ID for tracking
      const messageId = `digest_context_${format(date, 'yyyy-MM-dd')}_${Date.now()}`;
      const conversationId = `digest_${format(date, 'yyyy-MM-dd')}`;
      
      // Process the input through the same context extraction system as Lotus chat
      const { processMessageForContext } = require('../../utils/contextExtractor');
      await processMessageForContext(contextInput, conversationId, messageId);
      console.log('[DigestContextModal] Successfully processed input through context extractor');
      
      // Also save as a daily preferences entry for the digest system
      const contextDataToSave = {
        contextType: ContextType.PREFERENCE,
        value: contextInput,
        source: 'digest_page',
        timestamp: new Date().toISOString(),
        metadata: {
          category: 'daily_preferences',
          date: format(date, 'yyyy-MM-dd'),
          resetFuturePlans: resetFuturePlans,
          messageId: messageId,
          conversationId: conversationId,
          wakeUpTime: undefined as string | undefined
        }
      };

      // Check if the input contains wake-up time information and add explicit metadata
      const wakeUpTimeMatch = contextInput.match(/(?:i|I)\s*(?:wake|get)\s*(?:up|at)\s*(?:at)?\s*(\d{1,2})(?::(\d{2}))?\s*(am|pm)?/i);
      if (wakeUpTimeMatch) {
        console.log('[DigestContextModal] Detected wake-up time in input');
        let hours = parseInt(wakeUpTimeMatch[1]);
        const minutes = wakeUpTimeMatch[2] ? parseInt(wakeUpTimeMatch[2]) : 0;
        const period = wakeUpTimeMatch[3]?.toLowerCase();

        // Convert to 24-hour format
        if (period === 'pm' && hours < 12) {
          hours += 12;
        } else if (period === 'am' && hours === 12) {
          hours = 0;
        }

        const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        console.log(`[DigestContextModal] Extracted wake-up time: ${formattedTime}`);
        
        // Add explicit wake-up time metadata
        contextDataToSave.metadata.wakeUpTime = formattedTime;
      }

      console.log('[DigestContextModal] Saving daily preferences context:', JSON.stringify(contextDataToSave, null, 2));
      
      // Save the daily preferences context data
      const saveResult = await saveContextData(contextDataToSave);
      console.log('[DigestContextModal] Context save result:', saveResult);
      
      if (!saveResult) {
        throw new Error('Failed to save context data');
      }

      // Wait a moment to ensure the context is properly saved
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Verify the context was saved by checking if we can retrieve it
      console.log('[DigestContextModal] Verifying context was saved...');
      const { getContextData } = require('../../services/contextService');
      const allPreferences = await getContextData(ContextType.PREFERENCE, true); // Force refresh
      const allCustom = await getContextData(ContextType.CUSTOM, true); // Force refresh
      
      console.log(`[DigestContextModal] Total PREFERENCE context entries: ${allPreferences.length}`);
      console.log(`[DigestContextModal] Total CUSTOM context entries: ${allCustom.length}`);
      
      if (allPreferences.length === 0 && allCustom.length === 0) {
        console.warn('[DigestContextModal] Context verification failed - context not found in either type');
        
        // Log more details for debugging
        console.log(`[DigestContextModal] Looking for date: ${format(date, 'yyyy-MM-dd')}`);
        console.log(`[DigestContextModal] Recent PREFERENCE entries:`, allPreferences.slice(0, 3));
        console.log(`[DigestContextModal] Recent CUSTOM entries:`, allCustom.slice(0, 3));
        
        // Test the digest service context retrieval
        console.log('[DigestContextModal] Testing digest service context retrieval...');
        console.log('[DigestContextModal] Calling generateBasicActivities to test context retrieval...');
                 const testActivities = await generateBasicActivities(date);
        console.log(`[DigestContextModal] Test activities generated: ${testActivities.length}`);
        
        if (testActivities.length > 0) {
          const breakfastActivity = testActivities.find(a => a.type === 'meal' && a.scheduledTime.includes('T08:'));
          if (breakfastActivity) {
            console.log(`[DigestContextModal] Breakfast activity time: ${breakfastActivity.scheduledTime.split('T')[1]}`);
            if (!breakfastActivity.scheduledTime.includes('T08:00')) {
              console.log('[DigestContextModal] ✅ Custom timing detected in generated activities');
            } else {
              console.log('[DigestContextModal] ❌ Still using default 8:00 AM timing');
            }
          }
        }
      } else {
        console.log('[DigestContextModal] ✅ Context verification successful');
      }

      // Clear context cache before regeneration to ensure fresh data
      console.log('[DigestContextModal] Clearing context cache before regeneration...');
      try {
        // Clear local storage cache if there's a method for it
        const { getContextFromLocalStorage } = require('../../services/contextService');
        // Force a fresh fetch by clearing and refetching
        await getContextData(ContextType.PREFERENCE, true); // Force refresh
        await getContextData(ContextType.CUSTOM, true); // Force refresh
      } catch (cacheError) {
        console.warn('[DigestContextModal] Could not clear context cache:', cacheError);
      }

      // Call the API directly to regenerate the digest with the new context
      try {
        // Get the API URL
        const apiUrl = await getApiUrl();
        const dateString = format(date, 'yyyy-MM-dd');
        console.log(`[DigestContextModal] Regenerating digest for date: ${dateString} with new context`);

        // Force regeneration of the digest by calling the API
        const response = await fetch(`${apiUrl}/digest/${dateString}/regenerate`, {
          method: 'POST',
          headers: {
            ...await getAuthHeader(),
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            forceRegenerate: true,
            useGrok: true,
            includeContext: true,
            refreshContext: true // Add flag to refresh context
          }),
        });

        if (!response.ok) {
          throw new Error(`API regeneration failed with status ${response.status}`);
        }

        console.log('[DigestContextModal] Successfully regenerated digest with new context');
      } catch (apiError) {
        console.error('[DigestContextModal] Error calling API to regenerate digest:', apiError);

        // Fall back to local generation if API call fails
        console.log('[DigestContextModal] Falling back to local generation');
        const dateString = format(date, 'yyyy-MM-dd');
        
        // Force fresh context retrieval for local generation
        console.log('[DigestContextModal] Forcing fresh context retrieval for local generation');
        const activities = await generateBasicActivities(date);
        await saveDigestToAPI(dateString, activities);
      }

      // If resetFuturePlans is true, regenerate future plans
      if (resetFuturePlans) {
        // This would typically be handled by a backend process
        // For now, we'll just show a message
        console.log('[DigestContextModal] Future plans will be updated based on new preferences');
      }

      setIsLoading(false);
      onSuccess();
    } catch (error) {
      console.error('[DigestContextModal] Error saving context:', error);
      setError('Failed to save your preferences. Please try again.');
      setIsLoading(false);
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="none"
      onRequestClose={onClose}
    >
      <Animated.View
        entering={FadeIn.duration(300)}
        exiting={FadeOut.duration(300)}
        style={[styles.modalOverlay, { backgroundColor: 'rgba(0,0,0,0.5)' }]}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardView}
        >
          <Animated.View
            entering={SlideInDown.duration(400).springify()}
            exiting={SlideOutDown.duration(300)}
            style={[styles.modalContent, { backgroundColor: colors.card }]}
          >
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                {resetFuturePlans
                  ? 'Update Your Plan'
                  : 'Customize Today\'s Plan'}
              </Text>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.scrollContainer}>
              <Text style={[styles.instructionText, { color: colors.textSecondary }]}>
                {resetFuturePlans
                  ? 'Tell us your preferences to update today and future plans. For example, "I wake up at 9:00 AM" or "I want to focus on upper body workouts this week" or "I prefer to eat breakfast at 8:30 AM".'
                  : 'Tell us your preferences for today. For example, "I wake up at 9:00 AM" or "I want to do an upper body workout today" or "I\'d like to have pasta for dinner".'}
              </Text>
              <Text style={[styles.noteText, { color: colors.textSecondary, marginTop: 0, marginBottom: 16 }]}>
                <Text style={{ fontWeight: 'bold' }}>Important:</Text> To set your wake-up time, please include "I wake up at [time]" in your preferences.
              </Text>

              <TextInput
                style={[
                  styles.contextInput,
                  {
                    backgroundColor: isDark ? colors.background : '#f5f5f5',
                    color: colors.text,
                    borderColor: error ? colors.error : colors.border,
                  },
                ]}
                placeholder="Enter your preferences in natural language..."
                placeholderTextColor={colors.textTertiary}
                multiline
                numberOfLines={5}
                value={contextInput}
                onChangeText={setContextInput}
                editable={!isLoading}
              />

              {error && (
                <Text style={[styles.errorText, { color: colors.error }]}>
                  {error}
                </Text>
              )}

              <View style={styles.buttonContainer}>
                {isLoading ? (
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={colors.primary} />
                    <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                      Updating your plan...
                    </Text>
                  </View>
                ) : (
                  <>
                    <TouchableOpacity
                      style={[styles.submitButton, { backgroundColor: colors.primary }]}
                      onPress={handleSubmit}
                      disabled={!contextInput.trim()}
                    >
                      <Text style={[styles.submitButtonText, { color: colors.background }]}>
                        {resetFuturePlans ? 'Update Plans' : 'Update Today'}
                      </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[styles.cancelButton, { borderColor: colors.border }]}
                      onPress={onClose}
                    >
                      <Text style={[styles.cancelButtonText, { color: colors.text }]}>
                        Cancel
                      </Text>
                    </TouchableOpacity>
                  </>
                )}
              </View>

              {resetFuturePlans && (
                <Text style={[styles.noteText, { color: colors.textTertiary }]}>
                  Note: This will update your plan for today and influence future recommendations.
                </Text>
              )}
            </ScrollView>
          </Animated.View>
        </KeyboardAvoidingView>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  keyboardView: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  modalTitle: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.semibold,
    flex: 1,
  },
  closeButton: {
    padding: 4,
  },
  scrollContainer: {
    padding: 16,
  },
  instructionText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 16,
  },
  contextInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    minHeight: 120,
    textAlignVertical: 'top',
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 16,
  },
  errorText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 16,
  },
  buttonContainer: {
    marginTop: 8,
    marginBottom: 24,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  loadingText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    marginTop: 8,
  },
  submitButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 12,
  },
  submitButtonText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
  cancelButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    borderWidth: 1,
  },
  cancelButtonText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
  noteText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    fontStyle: 'italic',
    textAlign: 'center',
    marginBottom: 16,
  },
});

export default DigestContextModal;

import { WorkoutData, MealData, WorkoutExercise } from './conversationService';
import { api, makeRequestWithRetry } from './apiClient'; // Import the makeRequestWithRetry
import { getStoredTokens } from './auth'; // Add this import for authentication
import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Get the user ID from various sources
 *
 * @returns The user ID or null if not found
 */
export async function getUserId(): Promise<string | null> {
  try {
    console.log('[getUserId] Attempting to get user ID from multiple sources');

    // Try to get from direct storage first
    const userId = await AsyncStorage.getItem('user_id');
    if (userId) {
      console.log('[getUserId] Found user ID in direct storage');
      return userId;
    }

    // Try to get from user profile
    try {
      const userProfileString = await AsyncStorage.getItem('@user_profile');
      if (userProfileString) {
        const userProfile = JSON.parse(userProfileString);
        if (userProfile && userProfile.userId) {
          console.log('[getUserId] Found user ID in profile:', userProfile.userId);
          // Save it for future use
          await AsyncStorage.setItem('user_id', userProfile.userId);
          return userProfile.userId;
        }
      }
    } catch (profileError) {
      console.error('[getUserId] Error getting user ID from profile:', profileError);
    }

    // Try to extract from JWT token
    try {
      const tokens = await getStoredTokens();
      if (tokens && tokens.idToken) {
        // Parse the JWT token payload (second part between dots)
        const tokenParts = tokens.idToken.split('.');
        if (tokenParts.length >= 2) {
          try {
            // Base64 decode the payload - React Native compatible approach
            const base64Payload = tokenParts[1].replace(/-/g, '+').replace(/_/g, '/');

            // Add padding if needed
            const pad = base64Payload.length % 4;
            const paddedBase64 = pad ? base64Payload + '='.repeat(4 - pad) : base64Payload;

            // Use a React Native compatible base64 decoding approach
            const decodedPayload = decodeBase64(paddedBase64);
            const payload = JSON.parse(decodedPayload);

            // JWT could use different fields for the user ID
            const extractedId = payload.sub || payload.userId || payload.user_id || payload.cognito_id;
            if (extractedId) {
              console.log('[getUserId] Extracted user ID from token:', extractedId);
              // Save it for future use
              await AsyncStorage.setItem('user_id', extractedId);
              return extractedId;
            }
          } catch (decodeError) {
            console.warn('[getUserId] Error decoding token payload:', decodeError);
            // Continue to fallback methods
          }
        }
      }

      // Helper function to decode base64 in React Native
      function decodeBase64(str: string): string {
        // This is a simple base64 decoder that works in React Native
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
        let output = '';

        str = String(str).replace(/=+$/, '');

        if (str.length % 4 === 1) {
          throw new Error("'atob' failed: The string to be decoded is not correctly encoded.");
        }

        for (
          let bc = 0, bs = 0, buffer, i = 0;
          (buffer = str.charAt(i++));
          ~buffer && (bs = bc % 4 ? bs * 64 + buffer : buffer, bc++ % 4)
            ? (output += String.fromCharCode(255 & (bs >> ((-2 * bc) & 6))))
            : 0
        ) {
          buffer = chars.indexOf(buffer);
        }

        return output;
      }
    } catch (tokenError) {
      console.error('[getUserId] Error extracting user ID from token:', tokenError);
    }

    // If we still don't have a user ID, generate a temporary one
    const tempUserId = `temp_user_${Date.now()}`;
    console.log('[getUserId] No user ID found, using temporary ID:', tempUserId);
    await AsyncStorage.setItem('user_id', tempUserId);
    return tempUserId;
  } catch (error) {
    console.error('[getUserId] Error getting user ID:', error);
    // Return a fallback ID in case of error
    return `fallback_user_${Date.now()}`;
  }
}

// API URL - get this from environment variables in a real app
// const API_URL = "https://b18yfxhkj5.execute-api.us-east-1.amazonaws.com/prod/"; // URL is likely handled by the api instance baseURL

/**
 * Represents a message in the conversation with Gemini
 */
export interface Message {
  role: 'user' | 'assistant' | 'system' | 'model';
  content: string;
  timestamp?: string; // ISO string timestamp for when the message was sent
  context?: {
    date?: string; // ISO string date referenced by the message for context
    referencedWorkouts?: string[]; // IDs of workouts referenced in the message
  };
  detectedWorkout?: WorkoutData;
  detectedMeal?: MealData;
  multipleMeals?: MealData[]; // Array of meals when multiple meals are detected
}

/**
 * Represents structured data detected in Gemini responses
 */
export interface DetectedContent {
  hasWorkout: boolean;
  workout?: WorkoutData;
  hasMeal: boolean;
  meal?: MealData;
}

/**
 * Parse Gemini response to detect and structure workout and meal data
 *
 * @param text - The raw text response from Gemini
 * @returns Structured data about detected workouts and meals
 */
export function parseLlamaResponse(text: string): {
  text: string,
  hasMeal: boolean;
  meal?: MealData;
  multipleMeals?: MealData[];
  hasWorkout: boolean;
  workout?: WorkoutData;
  originalJson?: string
} {
  const structuredResult = {
    hasMeal: false,
    meal: undefined as MealData | undefined,
    multipleMeals: undefined as MealData[] | undefined,
    hasWorkout: false,
    workout: undefined as WorkoutData | undefined,
    originalJson: undefined as string | undefined
  };
  let cleanedText = text; // Start with the full text

  try {
    // Simplified JSON detection - focus on the most common patterns
    const jsonRegexOptions = [
      // Match JSON with meal data - common format in API responses
      /(\{[\s\S]*?"meal"[\s\S]*?\}\s*\})/g,
      // Match JSON with workout data - common format in API responses
      /(\{[\s\S]*?"workout"[\s\S]*?\}\s*\})/g,
      // Match code blocks with JSON
      /```(?:json)?\s*(\{[\s\S]*?\})\s*```/g,
      // Match the specific format seen in logs for meals
      /(\{\s*\n\s*"meal"[\s\S]*?\n\})/g,
      // Match the specific format seen in logs for workouts
      /(\{\s*\n\s*"workout"[\s\S]*?\n\})/g,
      // Match dietary preference JSON
      /(\{[\s\S]*?"dietaryPreference"[\s\S]*?\}\s*\})/g
    ];

    let jsonMatches: string[] = [];

    // Try each regex pattern
    for (const regex of jsonRegexOptions) {
      const matches = text.match(regex) || [];
      if (matches.length > 0) {
        // Found potential JSON matches
        jsonMatches = [...jsonMatches, ...matches];
      }
    }

    // If no matches found with regex, try direct extraction
    if (jsonMatches.length === 0 && (text.includes('"meal"') || text.includes('"workout"'))) {
      // Look for JSON structure with meal or workout data
      const startIndex = text.indexOf('{');
      if (startIndex !== -1) {
        // Find the matching closing brace
        let openBraces = 1;
        let endIndex = startIndex + 1;

        while (openBraces > 0 && endIndex < text.length) {
          if (text[endIndex] === '{') openBraces++;
          if (text[endIndex] === '}') openBraces--;
          endIndex++;
        }

        if (openBraces === 0) {
          const jsonCandidate = text.substring(startIndex, endIndex);
          jsonMatches.push(jsonCandidate);
        }
      }
    }

    // Special case: try to find JSON objects that span multiple lines
    // This helps with formatted JSON that might not be captured by the regex patterns above
    try {
      // Look for patterns like '{' followed by '"meal"' or '"ingredients"' within a reasonable distance
      const jsonStartIndices = [];
      let startBraceIndex = text.indexOf('{');
      while (startBraceIndex !== -1) {
        jsonStartIndices.push(startBraceIndex);
        startBraceIndex = text.indexOf('{', startBraceIndex + 1);
      }

      for (const startIndex of jsonStartIndices) {
        // Check if this opening brace is followed by meal or workout related keys
        const nextChunk = text.substring(startIndex, startIndex + 200);
        if (nextChunk.includes('"meal"') ||
            nextChunk.includes('"ingredients"') ||
            nextChunk.includes('"steps"') ||
            nextChunk.includes('"workout"') ||
            nextChunk.includes('"exercises"')) {
          // Try to find the matching closing brace
          let openBraces = 1;
          let endIndex = startIndex + 1;

          while (openBraces > 0 && endIndex < text.length) {
            if (text[endIndex] === '{') openBraces++;
            if (text[endIndex] === '}') openBraces--;
            endIndex++;
          }

          if (openBraces === 0) {
            // We found a complete JSON object
            const jsonCandidate = text.substring(startIndex, endIndex);
            jsonMatches.push(jsonCandidate);
          }
        }
      }
    } catch (e: any) {
      console.log('Error in advanced JSON detection:', e.message);
    }

    // Remove duplicates
    jsonMatches = [...new Set(jsonMatches)];

    console.log(`Found ${jsonMatches.length} potential JSON matches in response`);

    // If we found JSON matches, process them
    if (jsonMatches.length > 0) {
      console.log('JSON MATCHES:', jsonMatches.map(m => m.substring(0, 50) + '...'));

      for (const jsonMatch of jsonMatches) {
        try {
          // Clean up the JSON string by removing code block markers and any surrounding text
          let cleanedJson = jsonMatch.replace(/```(?:json)?\s*/, '').replace(/\s*```$/, '').trim();

          // Try to extract just the JSON object if there's text before or after it
          const jsonStartIndex = cleanedJson.indexOf('{');
          const jsonEndIndex = cleanedJson.lastIndexOf('}') + 1;

          if (jsonStartIndex > 0 || jsonEndIndex < cleanedJson.length) {
            // There's text before or after the JSON object
            if (jsonStartIndex >= 0 && jsonEndIndex > jsonStartIndex) {
              // Extract just the JSON part
              cleanedJson = cleanedJson.substring(jsonStartIndex, jsonEndIndex);
              console.log('Extracted JSON from surrounding text');
            }
          }

          console.log('Attempting to parse:', cleanedJson.substring(0, 100) + '...');

          // Try to parse the JSON
          let parsedData;
          try {
            // First, try to parse as-is
            parsedData = JSON.parse(cleanedJson);
          } catch (parseError: any) {
            console.log('Initial JSON parse failed:', parseError.message);

            // Try to fix common JSON issues
            let fixedJson = cleanedJson;

            // 1. Try to balance braces if needed
            const openBraces = (fixedJson.match(/\{/g) || []).length;
            const closeBraces = (fixedJson.match(/\}/g) || []).length;

            if (openBraces > closeBraces) {
              // Add missing closing braces
              for (let i = 0; i < openBraces - closeBraces; i++) {
                fixedJson += '}';
              }
              console.log('Added missing closing braces');
            }

            // 2. Try to extract just the meal object if it exists
            const mealMatch = fixedJson.match(/\{\s*"meal"[\s\S]*?\}\s*\}/);
            if (mealMatch) {
              fixedJson = mealMatch[0];
              console.log('Extracted meal object:', fixedJson.substring(0, 50) + '...');
            }

            // Try to parse the fixed JSON
            try {
              parsedData = JSON.parse(fixedJson);
              console.log('Successfully parsed fixed JSON');
            } catch (fixError: any) {
              console.log('Fixed JSON parse failed:', fixError.message);

              // Last resort: try to manually construct a meal object from the text
              if (fixedJson.includes('"meal"') && fixedJson.includes('"title"')) {
                try {
                  // Extract title
                  const titleMatch = fixedJson.match(/"title"\s*:\s*"([^"]+)"/);
                  const title = titleMatch ? titleMatch[1] : 'Recipe';

                  // Extract description
                  const descMatch = fixedJson.match(/"description"\s*:\s*"([^"]+)"/);
                  const description = descMatch ? descMatch[1] : 'A delicious recipe';

                  // Create a minimal meal object
                  parsedData = {
                    meal: {
                      title: title,
                      description: description,
                      ingredients: [],
                      steps: []
                    }
                  };

                  console.log('Created minimal meal object from text');
                } catch (e: any) {
                  console.log('Failed to create minimal meal object:', e.message);
                  continue;
                }
              } else {
                console.log('No meal data found in JSON');
                continue;
              }
            }
          }

          console.log('Successfully parsed JSON:', JSON.stringify(parsedData, null, 2).substring(0, 200) + '...');

          // Check for dietary preference
          if (parsedData.dietaryPreference) {
            // This is a dietary preference, not a meal or workout
            // We can handle this by adding it to the context
            const { addContextEntry, ContextType } = require('../services/contextService');
            addContextEntry({
              type: ContextType.DIETARY_RESTRICTION,
              value: parsedData.dietaryPreference,
              source: 'user_input',
            });
            cleanedText = cleanedText.replace(jsonMatch, '');
          }
          // Check for workout data
          if (parsedData.workout || (parsedData.title && Array.isArray(parsedData.exercises))) {
              // Basic validation/assignment
              const workoutCandidate = parsedData.workout || parsedData;
              if(workoutCandidate.title && Array.isArray(workoutCandidate.exercises)) {
                  // Ensure the workout data has all required fields
                  const validatedWorkout: WorkoutData = {
                      title: workoutCandidate.title,
                      exercises: workoutCandidate.exercises.map((exercise: any) => {
                          // Ensure each exercise has properly formatted sets
                          let formattedSets;

                          if (Array.isArray(exercise.sets)) {
                              // Sets is already an array, use it directly
                              formattedSets = exercise.sets;
                          } else if (typeof exercise.sets === 'number') {
                              // Convert numeric sets to an array of set objects
                              const numSets = exercise.sets;
                              formattedSets = [];
                              for (let i = 0; i < numSets; i++) {
                                  formattedSets.push({
                                      reps: exercise.reps || 10,
                                      weight: exercise.weight || 0
                                  });
                              }
                          } else {
                              // Default to a single set
                              formattedSets = [{
                                  reps: exercise.reps || 10,
                                  weight: exercise.weight || 0
                              }];
                          }

                          return {
                              name: exercise.name || 'Exercise',
                              sets: formattedSets,
                              notes: exercise.notes,
                              muscleGroup: exercise.muscleGroup
                          };
                      }),
                      duration: typeof workoutCandidate.duration === 'number' ? workoutCandidate.duration : 30,
                      notes: workoutCandidate.notes || '',
                      tags: Array.isArray(workoutCandidate.tags) ? workoutCandidate.tags : []
                  };

                  structuredResult.hasWorkout = true;
                  structuredResult.workout = validatedWorkout;
                  // Store the original JSON for later use
                  structuredResult.originalJson = jsonMatch;
                  console.log('✅ Detected workout data:', validatedWorkout.title);
                  console.log('WORKOUT DETAILS:', JSON.stringify({
                      title: validatedWorkout.title,
                      exercises: validatedWorkout.exercises ? validatedWorkout.exercises.length : 0,
                      duration: validatedWorkout.duration
                  }));

                  // Remove the JSON block from the text response
                  cleanedText = cleanedText.replace(jsonMatch, '');
              } else {
                  console.log('❌ Invalid workout data structure - missing title or exercises array');

                  // Try to fix the workout data if possible
                  if (workoutCandidate.title) {
                      // If we have a title but missing exercises, create a minimal array
                      const fixedWorkout: WorkoutData = {
                          title: workoutCandidate.title,
                          exercises: Array.isArray(workoutCandidate.exercises) ? workoutCandidate.exercises : [],
                          duration: typeof workoutCandidate.duration === 'number' ? workoutCandidate.duration : 30,
                          notes: workoutCandidate.notes || ''
                      };

                      // If we have at least one exercise, consider it valid
                      if (fixedWorkout.exercises && fixedWorkout.exercises.length > 0) {
                          structuredResult.hasWorkout = true;
                          structuredResult.workout = fixedWorkout;
                          console.log('✅ Fixed and detected workout data:', fixedWorkout.title);

                          // Remove the JSON block from the text response
                          cleanedText = cleanedText.replace(jsonMatch, '');
                      }
                  }
              }
          }

          // Check for meal data - more thorough detection with support for multiple meals
          if (
              // Check for the meal object directly
              parsedData.meal ||
              // Check for multiple meals array
              parsedData.meals ||
              // Or check if this is a recipe-like object at the root level
              (
                  parsedData.title &&
                  (
                      Array.isArray(parsedData.ingredients) ||
                      Array.isArray(parsedData.steps) ||
                      typeof parsedData.description === 'string'
                  )
              )
          ) {
              // Check for multiple meals first
              if (parsedData.meals && Array.isArray(parsedData.meals)) {
                  console.log(`Found multiple meals: ${parsedData.meals.length}`);

                  // Process each meal in the array
                  const validatedMeals: MealData[] = [];

                  for (const mealItem of parsedData.meals) {
                      // Validate it has at least the basic meal structure
                      if (mealItem.title) {
                          // Ensure the meal data has all required fields
                          const validatedMeal: MealData = {
                              title: mealItem.title,
                              description: mealItem.description || `${mealItem.title}`,
                              ingredients: Array.isArray(mealItem.ingredients) ? mealItem.ingredients : [],
                              steps: Array.isArray(mealItem.steps) ? mealItem.steps : [],
                              prepTime: typeof mealItem.prepTime === 'number' ? mealItem.prepTime : 0,
                              cookTime: typeof mealItem.cookTime === 'number' ? mealItem.cookTime : 0,
                              servings: typeof mealItem.servings === 'number' ? mealItem.servings : 0,
                              calories: typeof mealItem.calories === 'number' ? mealItem.calories : 0,
                              protein: typeof mealItem.protein === 'number' ? mealItem.protein : 0,
                              carbs: typeof mealItem.carbs === 'number' ? mealItem.carbs : 0,
                              fat: typeof mealItem.fat === 'number' ? mealItem.fat : 0
                          };

                          validatedMeals.push(validatedMeal);
                          console.log('✅ Detected meal in array:', validatedMeal.title);
                      }
                  }

                  // If we found at least one valid meal, store it
                  if (validatedMeals.length > 0) {
                      structuredResult.hasMeal = true;
                      structuredResult.meal = validatedMeals[0]; // Store the first meal as the primary
                      structuredResult.multipleMeals = validatedMeals; // Store all meals
                      structuredResult.originalJson = jsonMatch;

                      console.log(`✅ Processed ${validatedMeals.length} meals`);

                      // Remove the JSON block from the text response
                      cleanedText = cleanedText.replace(jsonMatch, '');
                  }
              } else {
                  // Handle single meal case
                  // Get the meal data, either from the meal property or the root object
                  const mealCandidate = parsedData.meal || parsedData;

                  console.log('Found meal candidate:', JSON.stringify(mealCandidate, null, 2).substring(0, 300));

                  // Validate it has at least the basic recipe structure
                  if(
                      mealCandidate.title &&
                      (
                          Array.isArray(mealCandidate.ingredients) ||
                          Array.isArray(mealCandidate.steps) ||
                          typeof mealCandidate.description === 'string'
                      )
                  ) {
                      // Ensure the meal data has all required fields
                      const validatedMeal: MealData = {
                          title: mealCandidate.title,
                          description: mealCandidate.description || `${mealCandidate.title}`,
                          ingredients: Array.isArray(mealCandidate.ingredients) ? mealCandidate.ingredients : [],
                          steps: Array.isArray(mealCandidate.steps) ? mealCandidate.steps : [],
                          prepTime: typeof mealCandidate.prepTime === 'number' ? mealCandidate.prepTime : 0,
                          cookTime: typeof mealCandidate.cookTime === 'number' ? mealCandidate.cookTime : 0,
                          servings: typeof mealCandidate.servings === 'number' ? mealCandidate.servings : 0,
                          calories: typeof mealCandidate.calories === 'number' ? mealCandidate.calories : 0,
                          protein: typeof mealCandidate.protein === 'number' ? mealCandidate.protein : 0,
                          carbs: typeof mealCandidate.carbs === 'number' ? mealCandidate.carbs : 0,
                          fat: typeof mealCandidate.fat === 'number' ? mealCandidate.fat : 0
                      };

                      structuredResult.hasMeal = true;
                      structuredResult.meal = validatedMeal;
                      structuredResult.multipleMeals = [validatedMeal]; // Store as an array with one meal
                      // Store the original JSON for later use
                      structuredResult.originalJson = jsonMatch;
                      console.log('✅ Detected meal data:', validatedMeal.title);
                      console.log('MEAL DETAILS:', JSON.stringify({
                          title: validatedMeal.title,
                          ingredients: validatedMeal.ingredients ? validatedMeal.ingredients.length : 0,
                          steps: validatedMeal.steps ? validatedMeal.steps.length : 0
                      }));

                      // Remove the JSON block from the text response
                      cleanedText = cleanedText.replace(jsonMatch, '');
                  } else {
                      console.log('❌ Invalid meal data structure - missing title or ingredients/steps/description');

                      // Try to fix the meal data if possible
                      if (mealCandidate.title) {
                          // If we have a title but missing ingredients or steps, create minimal arrays
                          const fixedMeal: MealData = {
                              title: mealCandidate.title,
                              description: mealCandidate.description || `${mealCandidate.title}`,
                              ingredients: Array.isArray(mealCandidate.ingredients) ? mealCandidate.ingredients : [],
                              steps: Array.isArray(mealCandidate.steps) ? mealCandidate.steps : [],
                              calories: typeof mealCandidate.calories === 'number' ? mealCandidate.calories : 0,
                              protein: typeof mealCandidate.protein === 'number' ? mealCandidate.protein : 0,
                              carbs: typeof mealCandidate.carbs === 'number' ? mealCandidate.carbs : 0,
                              fat: typeof mealCandidate.fat === 'number' ? mealCandidate.fat : 0
                          };

                          structuredResult.hasMeal = true;
                          structuredResult.meal = fixedMeal;
                          structuredResult.multipleMeals = [fixedMeal]; // Store as an array with one meal
                          console.log('✅ Fixed and detected meal data:', fixedMeal.title);

                          // Remove the JSON block from the text response
                          cleanedText = cleanedText.replace(jsonMatch, '');
                      }
                  }
              }
          }

        } catch (error: any) {
          console.log('Error processing JSON match:', error.message);
          continue;
        }
      }
    } else {
      // If no JSON matches found, try to extract structured data from plain text format

      // Check if this looks like a recipe (has "Ingredients:" and either "Instructions:" or "Directions:" or "Steps:")
      const isRecipe = /ingredients:|\*\*ingredients\*\*|ingredients/i.test(text) &&
                      (/instructions:|directions:|steps:|preparation:|method:|\*\*instructions\*\*|\*\*steps\*\*|\*\*directions\*\*/i.test(text));

      // Check if this looks like a workout (has "Exercises:" and either "Sets:" or "Reps:")
      const isWorkout = /workout plan|exercise routine|workout routine|exercises:|\*\*exercises\*\*|exercises/i.test(text) &&
                      (/sets:|reps:|repetitions:|\*\*sets\*\*|\*\*reps\*\*|\*\*repetitions\*\*/i.test(text));

      if (isWorkout) {
        // Try to extract workout data from plain text
        let workoutTitle = "Workout Plan";

        // Try to find a title in bold markdown or between the first line and Exercises
        const boldTitleMatch = text.match(/\*\*([^*\n]+?)\*\*/);
        const secondLineMatch = text.match(/^[^\n]+\n+\s*([^\n]+?)\s*\n/);
        const beforeExercisesMatch = text.match(/^[\s\S]*?\n([^\n]+?)\s*\n[^\n]*?(?:exercises:|\*\*exercises\*\*)/i);

        if (boldTitleMatch) {
          workoutTitle = boldTitleMatch[1].trim();
          console.log("Found bold workout title:", workoutTitle);
        } else if (secondLineMatch) {
          workoutTitle = secondLineMatch[1].trim();
          console.log("Found second line workout title:", workoutTitle);
        } else if (beforeExercisesMatch) {
          workoutTitle = beforeExercisesMatch[1].trim();
          console.log("Found title before exercises:", workoutTitle);
        } else {
          // Fallback to first line
          const titleMatch = text.match(/^([^\n]+?)(?:\n|$)/);
          if (titleMatch) {
            workoutTitle = titleMatch[1].trim();
            console.log("Using first line as workout title:", workoutTitle);
          }
        }

        // Clean up the title
        workoutTitle = workoutTitle.replace(/^here's\s+a\s+|^here\s+is\s+a\s+|^workout\s+for\s+|^workout:\s+/i, "");
        workoutTitle = workoutTitle.replace(/\*\*/g, "").trim();

        // Extract exercises
        const exercisesRegex = /(?:exercises:|\*\*exercises(?:\*\*)?:?)(?:\s*)([\s\S]*?)(?:(?:notes|tips|advice):|\*\*notes|\*\*tips|\n\s*\n\s*\*\*|$)/i;
        const exercisesMatch = text.match(exercisesRegex);
        let exercisesList: WorkoutExercise[] = [];

        if (exercisesMatch && exercisesMatch[1]) {
          // Split by new lines and bullet points
          const exerciseLines = exercisesMatch[1].split(/\n/)
            .map(line => line.trim())
            .filter(line => line && line.length > 1); // Keep non-empty lines

          // Process exercise lines to extract exercises and sets
          let currentExercise: WorkoutExercise | null = null;

          for (const line of exerciseLines) {
            // Check if this is a new exercise (starts with bullet or number)
            const isNewExercise = /^[•\-\*]\s*|^\d+\.\s+|^\*\*|^[A-Z]/.test(line);

            if (isNewExercise) {
              // If we have a current exercise, add it to the list
              if (currentExercise) {
                exercisesList.push(currentExercise);
              }

              // Create a new exercise
              const cleanedLine = line.replace(/^[•\-\*]\s*|^\d+\.\s+|^\*\*|\*\*$|^\s*\*\s*/, '');
              currentExercise = {
                name: cleanedLine,
                sets: []
              };

              // Try to extract sets/reps information from the exercise name
              const setsRepsMatch = cleanedLine.match(/(\d+)\s*(?:sets?|x)\s*(?:of)?\s*(\d+)\s*(?:reps?|repetitions?)/i);
              if (setsRepsMatch) {
                const numSets = parseInt(setsRepsMatch[1]);
                const numReps = parseInt(setsRepsMatch[2]);

                // Create the sets
                for (let i = 0; i < numSets; i++) {
                  currentExercise.sets.push({ reps: numReps });
                }
              }
            } else if (currentExercise) {
              // This is additional information for the current exercise
              // Check if it contains sets/reps information
              const setsRepsMatch = line.match(/(\d+)\s*(?:sets?|x)\s*(?:of)?\s*(\d+)\s*(?:reps?|repetitions?)/i);
              if (setsRepsMatch) {
                const numSets = parseInt(setsRepsMatch[1]);
                const numReps = parseInt(setsRepsMatch[2]);

                // Create the sets
                currentExercise.sets = [];
                for (let i = 0; i < numSets; i++) {
                  currentExercise.sets.push({ reps: numReps });
                }
              } else {
                // This might be notes for the exercise
                currentExercise.notes = (currentExercise.notes ? currentExercise.notes + " " : "") + line;
              }
            }
          }

          // Add the last exercise if we have one
          if (currentExercise) {
            // If no sets were added, add a default set
            if (currentExercise.sets.length === 0) {
              currentExercise.sets = [{ reps: 10 }];
            }
            exercisesList.push(currentExercise);
          }
        }

        // Extract notes
        const notesRegex = /(?:notes:|tips:|advice:|\*\*notes(?:\*\*)?:?)(?:\s*)([\s\S]*?)(?:\n\s*\n\s*(?:[^:\s])|\n\s*\n\s*\*\*|$)/i;
        const notesMatch = text.match(notesRegex);
        let workoutNotes = "";

        if (notesMatch && notesMatch[1]) {
          workoutNotes = notesMatch[1].trim();
        }

        // Create the workout object if we have at least one exercise
        if (exercisesList.length > 0) {
          const extractedWorkout: WorkoutData = {
            title: workoutTitle,
            exercises: exercisesList,
            notes: workoutNotes,
            duration: 30, // Default duration
            tags: []
          };

          structuredResult.hasWorkout = true;
          structuredResult.workout = extractedWorkout;

          // Keep the original text - don't remove anything
        }
      } else if (isRecipe) {

        // Try to extract a better title - look for a recipe name that might be in bold or on its own line
        let recipeTitle = "Recipe";

        // Try to find a title in bold markdown or between the first line and Ingredients
        const boldTitleMatch = text.match(/\*\*([^*\n]+?)\*\*/);
        const secondLineMatch = text.match(/^[^\n]+\n+\s*([^\n]+?)\s*\n/);
        const beforeIngredientsMatch = text.match(/^[\s\S]*?\n([^\n]+?)\s*\n[^\n]*?(?:ingredients:|\*\*ingredients\*\*)/i);

        if (boldTitleMatch) {
          recipeTitle = boldTitleMatch[1].trim();
          console.log("Found bold title:", recipeTitle);
        } else if (secondLineMatch) {
          recipeTitle = secondLineMatch[1].trim();
          console.log("Found second line title:", recipeTitle);
        } else if (beforeIngredientsMatch) {
          recipeTitle = beforeIngredientsMatch[1].trim();
          console.log("Found title before ingredients:", recipeTitle);
        } else {
          // Fallback to first line
          const titleMatch = text.match(/^([^\n]+?)(?:\n|$)/);
          if (titleMatch) {
            recipeTitle = titleMatch[1].trim();
            console.log("Using first line as title:", recipeTitle);
          }
        }

        // Clean up the title
        recipeTitle = recipeTitle.replace(/^here's\s+a\s+|^here\s+is\s+a\s+|^recipe\s+for\s+|^recipe:\s+/i, "");
        recipeTitle = recipeTitle.replace(/\*\*/g, "").trim();

        // Extract ingredients - handle both regular and markdown formatting
        const ingredientsRegex = /(?:ingredients:|\*\*ingredients(?:\*\*)?:?)(?:\s*)([\s\S]*?)(?:(?:instructions|directions|steps|preparation|method):|\*\*instructions|\*\*steps|\*\*directions|\n\s*\n\s*\*\*|$)/i;
        const ingredientsMatch = text.match(ingredientsRegex);
        let ingredientsList: string[] = [];

        if (ingredientsMatch && ingredientsMatch[1]) {
          // Split by new lines and bullet points, handling markdown formatting
          ingredientsList = ingredientsMatch[1].split(/\n/)
            .map(line => line.trim())
            .filter(line => line && line.length > 1) // Keep non-empty lines
            .map(line => line.replace(/^[•\-\*]\s*|^\d+\.\s+|^\*\*|\*\*$|^\s*\*\s*/, '')); // Remove bullet markers, numbers, and markdown
        }

        // Extract steps - handle both regular and markdown formatting
        const stepsRegex = /(?:instructions|directions|steps|preparation|method):(?:\s*)([\s\S]*?)(?:\n\s*\n\s*(?:[^:\s])|\n\s*\n\s*\*\*|$)/i;
        const markdownStepsRegex = /\*\*(?:instructions|steps|directions)(?:\*\*)?:?(?:\s*)([\s\S]*?)(?:\n\s*\n\s*(?:[^:\s])|\n\s*\n\s*\*\*|$)/i;

        const stepsMatch = text.match(stepsRegex) || text.match(markdownStepsRegex);
        let stepsList: string[] = [];

        if (stepsMatch && stepsMatch[1]) {
          // Split by new lines and numbers
          stepsList = stepsMatch[1].split(/\n/)
            .map(line => line.trim())
            .filter(line => line && line.length > 1) // Keep non-empty lines
            .map(line => line.replace(/^\d+[.)\s]+|^\*\*|\*\*$|^\s*\*\s*/, '')); // Remove step numbers and markdown
        }

        // If we still don't have ingredients or steps, try a more aggressive approach
        if (ingredientsList.length === 0) {
          // Look for bullet points or numbered lists
          const bulletListMatch = text.match(/(?:[\n\r]|^)\s*[•\-\*]\s*([^\n]+)(?:[\n\r]\s*[•\-\*]\s*[^\n]+)*/g);
          if (bulletListMatch) {
            ingredientsList = bulletListMatch[0].split(/\n/)
              .map(line => line.trim())
              .filter(line => line && line.length > 1)
              .map(line => line.replace(/^[•\-\*]\s*/, ''));
          }
        }

        if (stepsList.length === 0) {
          // Look for numbered lists
          const numberedListMatch = text.match(/(?:[\n\r]|^)\s*\d+[.)\s]+([^\n]+)(?:[\n\r]\s*\d+[.)\s]+[^\n]+)*/g);
          if (numberedListMatch) {
            stepsList = numberedListMatch[0].split(/\n/)
              .map(line => line.trim())
              .filter(line => line && line.length > 1)
              .map(line => line.replace(/^\d+[.)\s]+/, ''));
          }
        }

        // Proceed only if we have both ingredients and steps
        if (ingredientsList.length > 0 && stepsList.length > 0) {
          // Try to extract a better description
          let description = `A delicious ${recipeTitle} recipe with ${ingredientsList.length} ingredients.`;

          // Look for any description-like text before ingredients
          const descriptionMatch = text.match(/^[\s\S]*?\n\n([^\n]+?)\n\n[\s\S]*?(?:ingredients:|\*\*ingredients)/i);
          if (descriptionMatch && descriptionMatch[1] && descriptionMatch[1].length > 20) {
            description = descriptionMatch[1].trim();
          }

          // Try to extract nutritional information
          let calories = 0, protein = 0, carbs = 0, fat = 0, servings = 4;

          // Look for calorie information
          const caloriesMatch = text.match(/calories:?\s*(\d+)/i) || text.match(/\b(\d+)\s*calories\b/i);
          if (caloriesMatch) calories = parseInt(caloriesMatch[1]);

          // Look for protein information
          const proteinMatch = text.match(/protein:?\s*(\d+)\s*g/i) || text.match(/\b(\d+)\s*g\s*(?:of)?\s*protein\b/i);
          if (proteinMatch) protein = parseInt(proteinMatch[1]);

          // Look for carbs information
          const carbsMatch = text.match(/carbs:?\s*(\d+)\s*g/i) || text.match(/\b(\d+)\s*g\s*(?:of)?\s*carbs\b/i);
          if (carbsMatch) carbs = parseInt(carbsMatch[1]);

          // Look for fat information
          const fatMatch = text.match(/fat:?\s*(\d+)\s*g/i) || text.match(/\b(\d+)\s*g\s*(?:of)?\s*fat\b/i);
          if (fatMatch) fat = parseInt(fatMatch[1]);

          // Look for servings information
          const servingsMatch = text.match(/servings:?\s*(\d+)/i) || text.match(/serves\s*(\d+)/i);
          if (servingsMatch) servings = parseInt(servingsMatch[1]);

          // Build a meal object
          const extractedMeal: MealData = {
            title: recipeTitle,
            description: description,
            ingredients: ingredientsList,
            steps: stepsList,
            prepTime: 15, // Default values
            cookTime: 25,
            servings: servings,
            calories: calories,
            protein: protein,
            carbs: carbs,
            fat: fat
          };

          structuredResult.hasMeal = true;
          structuredResult.meal = extractedMeal;

          // Keep the original text - don't remove anything
        }
      }
    }
  } catch (error: any) {
    console.log('Error in parseLlamaResponse:', error.message);
  }

  // Preserve more of the original content - only do minimal cleanup
  // We want to keep the recipe text visible to the user
  cleanedText = removeMarkdown(cleanedText
    // Only remove JSON objects, not the surrounding text
    .replace(/\{\s*"meal"\s*:\s*\{[\s\S]*?\}\s*\}/g, '')
    .replace(/\{\s*\n\s*"meal"[\s\S]*?\n\}/g, '') // Special case for the format in the logs
    .replace(/\{\s*"workout"\s*:\s*\{[\s\S]*?\}\s*\}/g, '')
    // Remove any trailing closing braces that might be left over from JSON
    .replace(/\s*\}\s*$/g, '')
    // Clean up whitespace but preserve content
    .replace(/\n{3,}/g, '\n\n')
    .trim());

  // Add fallback text if needed
  if (cleanedText.length < 20) {
      if (structuredResult.hasMeal && structuredResult.meal) {
          // Create a more natural fallback text for recipes
          const mealTitle = structuredResult.meal.title || 'recipe';
          const fallbackOptions = [
              `Here's a ${mealTitle} for you.`,
              `This ${mealTitle} is ready to try.`,
              `Check out this ${mealTitle}.`,
              `Your ${mealTitle} is ready.`
          ];
          // Pick a random fallback text to avoid repetition
          cleanedText = fallbackOptions[Math.floor(Math.random() * fallbackOptions.length)];
      } else if (structuredResult.hasWorkout && structuredResult.workout) {
          const workoutTitle = structuredResult.workout.title || 'workout plan';
          const fallbackOptions = [
              `Here's your ${workoutTitle}. Details are below.`,
              `Your ${workoutTitle} is ready to try.`,
              `This ${workoutTitle} is all set for you.`,
              `Check out this ${workoutTitle} below.`
          ];
          cleanedText = fallbackOptions[Math.floor(Math.random() * fallbackOptions.length)];
      }
  }

  return {
      text: cleanedText,
      ...structuredResult
  };
}

/**
 * Removes markdown formatting from a string
 *
 * @param text Text potentially containing markdown
 * @returns Text without markdown formatting
 */
const removeMarkdown = (text: string): string => {
  if (!text) return '';

  // Less aggressive markdown removal to preserve more content
  return text
    // Remove headers but keep the text
    .replace(/^#{1,6}\s+(.*?)$/gm, '$1')
    // Remove bold/italic formatting but keep the text
    .replace(/\*\*(.*?)\*\*/g, '$1')
    .replace(/__(.*?)__/g, '$1')
    .replace(/\*(.*?)\*/g, '$1')
    .replace(/_(.*?)_/g, '$1')
    // Remove code block markers but keep the content
    .replace(/```(?:[\w]*\n)?/g, '')
    .replace(/```$/gm, '')
    // Remove inline code markers but keep the content
    .replace(/`([^`]+)`/g, '$1')
    // Convert bullet points to plain text with proper spacing
    .replace(/^\s*[\*\-\+]\s+/gm, '• ')
    // Convert numbered lists to plain text
    .replace(/^\s*(\d+)\.\s+/gm, '$1. ')
    // Remove blockquotes
    .replace(/^\s*>\s+(.*?)$/gm, '$1')
    // Remove image syntax but keep alt text
    .replace(/!\[(.*?)\]\(.*?\)/g, '$1')
    // Remove link syntax but keep link text
    .replace(/\[(.*?)\]\(.*?\)/g, '$1')
    // Remove HTML tags
    .replace(/<\/?[a-z][^>]*>/gi, '')
    // Remove strikethrough formatting but keep text
    .replace(/~~(.*?)~~/g, '$1')
    // Clean up any escaped characters
    .replace(/\\([\\`*_{}[\]()#+-.!])/g, '$1');
};

/**
 * Sends a message to Gemini and returns the response
 */
export async function sendMessageToChat(
  messages: Message[],
  userId: string,
  conversationId: string,
  options?: { temperature?: number }
): Promise<Message> {
  try {
    // Start with a detailed log header to make this call easy to find in logs
    console.log(`\n[CHAT API] =============== START CHAT API CALL ===============`);
    console.log(`[CHAT API] Time: ${new Date().toISOString()}`);
    console.log(`[CHAT API] Messages: ${messages.length}`);
    console.log(`[CHAT API] User ID: ${userId || 'MISSING'}`);
    console.log(`[CHAT API] Conversation ID: ${conversationId || 'MISSING'}`);

    if (!userId) {
      console.warn('[CHAT API] CRITICAL - userId is empty in sendMessageToChat call');
      throw new Error('Missing userId parameter');
    }

    if (!conversationId) {
      console.error('[CHAT API] CRITICAL - Missing conversationId in sendMessageToChat');
      throw new Error('Missing conversationId parameter');
    }

    // Immediately write the user message to context before making the API call
    // This ensures the message is stored even if the API call fails
    try {
      // Get the latest user message
      const latestUserMessage = messages[messages.length - 1];
      if (latestUserMessage && latestUserMessage.role === 'user') {
        // Generate a unique message ID that includes the conversation ID for better tracking
        const messageId = `chat_${conversationId}_${Date.now()}`;

        console.log(`[CHAT API] Processing user message with ID: ${messageId}`);

        // Use the new streamlined context extraction process
        const { analyzeMessage } = require('../services/messageAnalysisService');
        await analyzeMessage(
          latestUserMessage.content,
          conversationId,
          messageId
        );

        console.log('[CHAT API] Successfully processed user message for context');
      }
    } catch (contextError: any) {
      console.error('[CHAT API] Error processing user message for context:', contextError);
      // If context extraction fails, we should not proceed with the API call
      // as it may result in a response that doesn't respect the user's latest preferences.
      throw new Error('Failed to process your message for context. Please try again.');
    }

    // Get auth headers explicitly
    const tokens = await getStoredTokens();
    if (!tokens || !tokens.idToken) {
      console.error('[CHAT API] CRITICAL - No valid auth tokens found for chat request');
      throw new Error('Authentication error: Not authenticated');
    }

    console.log('[CHAT API] Authentication verified, token retrieved successfully');

    // Get user profile and context information using the new context engine
    let userProfileInfo = '';
    let userContextInfo = '';

    try {
      // Get user profile
      const { getProfileForContext } = require('../context/ProfileContext');
      const profile = await getProfileForContext();

      if (profile) {
        // Calculate age if birthday is available
        let ageInfo = '';
        if (profile.birthday) {
          const birthDate = new Date(profile.birthday);
          const today = new Date();
          let age = today.getFullYear() - birthDate.getFullYear();
          const monthDiff = today.getMonth() - birthDate.getMonth();
          if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
            age--;
          }
          ageInfo = `Age: ${age} years old\n`;
        }

        userProfileInfo = `
USER PROFILE INFORMATION:
Name: ${profile.name || 'Not provided'}
${ageInfo}Weight: ${profile.weight ? `${profile.weight} lbs` : 'Not provided'}
Height: ${profile.height || 'Not provided'}
Fitness Goal: ${profile.fitnessGoal || 'Not provided'}
`;
      }

      // Use the new comprehensive context engine
      // For new conversations or if this is a preference-related query, force refresh
      const isNewConversation = messages.length === 1;
      const containsPreferenceQuery = messages.some(msg => 
        msg.content.toLowerCase().includes('thoughts about') ||
        msg.content.toLowerCase().includes('what do you think') ||
        msg.content.toLowerCase().includes('recommend') ||
        msg.content.toLowerCase().includes('suggest')
      );
      
      const shouldForceRefresh = isNewConversation || containsPreferenceQuery;
      
      console.log(`[CHAT API] Context refresh: isNewConversation=${isNewConversation}, containsPreferenceQuery=${containsPreferenceQuery}, shouldForceRefresh=${shouldForceRefresh}`);
      
      // Use the new Context Engine V3 for comprehensive user context
      const { getUserContextForLLM } = require('./contextEngineV3');
      const contextResult = await getUserContextForLLM(shouldForceRefresh);

      userContextInfo = contextResult.contextText;

      // Log context being sent to LLM for debugging
      console.log('[CHAT API] Using ContextEngineV3 for comprehensive user context');
      console.log('[CHAT API] Context info size:', contextResult.totalLength);
      console.log('[CHAT API] Total entries:', contextResult.totalEntries);
      console.log('[CHAT API] Critical context included:', contextResult.criticalDataIncluded);
      console.log('[CHAT API] Included context types:', contextResult.includedTypes.join(', '));
      console.log('[CHAT API] Context version:', contextResult.version);
      if (contextResult.errors.length > 0) {
        console.warn('[CHAT API] Context errors:', contextResult.errors);
      }
      console.log('[CHAT API] Context info preview:', userContextInfo.substring(0, 200) + '...');
    } catch (error) {
      console.error('[CHAT API] Error getting user profile or context:', error);
      // Continue without profile/context info
    }

    // Add system prompt to the messages
    let systemPrompt = getSystemPrompt();

    // Add user profile and context information to the system prompt if available
    if (userProfileInfo || userContextInfo) {
      systemPrompt = `${systemPrompt}\n\n${userProfileInfo}\n${userContextInfo}`;
    }

    console.log('[CHAT API] Adding system prompt with user context:', systemPrompt.substring(0, 50) + '...');
    
    // Enhanced logging for debugging context issues
    console.log('[CHAT API] FULL SYSTEM PROMPT LENGTH:', systemPrompt.length);
    console.log('[CHAT API] SYSTEM PROMPT PREVIEW (first 500 chars):');
    console.log(systemPrompt.substring(0, 500));
    console.log('[CHAT API] SYSTEM PROMPT CONTEXT SECTION (last 1000 chars):');
    console.log(systemPrompt.substring(Math.max(0, systemPrompt.length - 1000)));
    
    // Check for specific preference indicators in the system prompt
    const hasQuinoaDislike = systemPrompt.toLowerCase().includes('quinoa');
    const hasDumbbellLove = systemPrompt.toLowerCase().includes('dumbbell');
    const hasFoodDislikes = systemPrompt.includes('FOOD DISLIKES');
    const hasUserPreferences = systemPrompt.includes('USER PREFERENCES');
    
    console.log('[CHAT API] SYSTEM PROMPT CONTENT ANALYSIS:', {
      hasQuinoaDislike,
      hasDumbbellLove,
      hasFoodDislikes,
      hasUserPreferences,
      totalLength: systemPrompt.length,
      hasUserContext: !!userContextInfo,
      userContextLength: userContextInfo?.length || 0
    });

    const messagesWithSystemPrompt = [
      { role: 'system', content: systemPrompt },
      ...messages.map(msg => ({
        role: msg.role === 'model' ? 'assistant' : msg.role,
        content: msg.content,
      }))
    ];

    console.log(`[CHAT API] Prepared ${messagesWithSystemPrompt.length} messages for API (including system prompt)`);

    // Create an abort controller for manual timeout handling
    const controller = new AbortController();

    // Check if this is the first message in a conversation
    const isFirstMessage = messages.length === 1 && messages[0].role === 'user';

    // Use longer timeout for first messages
    const timeoutDuration = isFirstMessage ? 45000 : 30000; // 45 seconds for first message, 30 for others

    // Set timeout to give more time for the model to respond
    const timeoutId = setTimeout(() => {
      console.log(`[CHAT API] Manual timeout reached (${timeoutDuration/1000}s), aborting request`);
      controller.abort(`Request timeout after ${timeoutDuration/1000} seconds`);
    }, timeoutDuration);

    try {
      console.log('[CHAT API] Beginning API request with retry capability');

      // We already checked if this is the first message above

      // Use enhanced retry settings for first messages
      const retrySettings = isFirstMessage
        ? {
            maxRetries: 5, // More retries for first message
            retryDelay: 3000, // Longer delay between retries
            shouldRetry: (error: any) => {
              // For first messages, retry on more error types including server errors
              const shouldRetry = error.message.includes('Network Error') ||
                                 error.code === 'ECONNABORTED' ||
                                 !error.response ||
                                 (error.response && error.response.status >= 500);

              console.log(`[CHAT API] First message error: ${error.message}. Retry? ${shouldRetry}`);
              return shouldRetry;
            }
          }
        : {
            maxRetries: 2, // Standard retries for subsequent messages
            retryDelay: 2000, // Standard delay
            shouldRetry: (error: any) => {
              // Only retry network errors and timeouts for regular messages
              const shouldRetry = error.message.includes('Network Error') ||
                                 error.code === 'ECONNABORTED' ||
                                 !error.response;

              console.log(`[CHAT API] Error occurred: ${error.message}. Retry? ${shouldRetry}`);
              return shouldRetry;
            }
          };

      // Log special handling for first message
      if (isFirstMessage) {
        console.log('[CHAT API] First message in conversation detected - using enhanced retry settings');
      }

      // Use our retry-enabled request method with enhanced error handling
      try {
        const response = await makeRequestWithRetry({
          url: '/chat',
          method: 'POST',
          data: {
            messages: messagesWithSystemPrompt,
            userId,
            conversationId,
            temperature: options?.temperature || 0.7,
            isFirstMessage: isFirstMessage // Add flag to help server identify first messages
          },
          timeout: isFirstMessage ? 60000 : 45000, // Longer timeout for first message
          signal: controller.signal
        }, retrySettings);

        // Clear the timeout since the request completed successfully
        clearTimeout(timeoutId);

        console.log('[CHAT API] Successfully received response!');
        console.log(`[CHAT API] Response status: ${response.status}`);
        console.log(`[CHAT API] Response size: ${JSON.stringify(response.data).length} bytes`);

        // Process the response data
        const responseData = response.data;

        // Store the original content for JSON detection
        const originalContent = responseData.content || '';
        console.log(`[CHAT API] Response content length: ${originalContent.length} chars`);

        // Parse structured data to detect meal/workout
        const parsedData = parseLlamaResponse(originalContent);

        // Log detection results if found
        if (parsedData.hasMeal || parsedData.hasWorkout) {
          console.log('[CHAT API] Structured data detected:', {
            meal: parsedData.hasMeal ? parsedData.meal?.title : 'None',
            workout: parsedData.hasWorkout ? parsedData.workout?.title : 'None'
          });
        }

        // Create display content with minimal cleanup
        let displayContent = removeMarkdown(parsedData.text);

        // Remove any trailing JSON artifacts and fix any quote issues
        displayContent = displayContent
          .replace(/\s*\}\s*$/g, '')
          .replace(/^"([\s\S]+)"$/g, '$1')  // Remove surrounding quotes that might be in the response (handles multiline)
          .trim();

        // Create a new message object with our parsed data
        const newMessage: Message = {
          role: 'assistant',
          content: displayContent, // Use the cleaned content
          timestamp: new Date().toISOString(),
          detectedWorkout: parsedData.hasWorkout ? parsedData.workout : undefined,
          detectedMeal: parsedData.hasMeal ? parsedData.meal : undefined,
          multipleMeals: parsedData.multipleMeals,
        };

        console.log('[CHAT API] Created new message object:', {
          role: newMessage.role,
          contentLength: newMessage.content.length,
          timestamp: newMessage.timestamp,
          hasMeal: !!newMessage.detectedMeal,
          hasWorkout: !!newMessage.detectedWorkout
        });

        // Process the assistant's response for context
        try {
          // Generate a unique message ID for the assistant's response
          const assistantMessageId = `assistant_${conversationId}_${Date.now()}`;

          // Store the assistant's message in context
          const { addContextEntry, ContextType } = require('../services/contextService');
          await addContextEntry({
            type: ContextType.MESSAGE_CONTENT,
            value: newMessage.content,
            source: 'assistant',
            messageId: assistantMessageId,
            conversationId: conversationId,
            metadata: { role: 'assistant' }
          });
          console.log('[CHAT API] Stored assistant message in context');

          // Process the entire conversation for summary
          // We will rely on the individual message analysis for now
          // and the narrative summary from the context service.
          // const { processConversationForContext } = require('../utils/contextExtractor');
          // await processConversationForContext([...messages, newMessage], conversationId);
          // console.log('[CHAT API] Generated conversation summary');

          // Check and update achievements after conversation
          try {
            const { checkAndUpdateAchievements } = require('./achievementService');
            checkAndUpdateAchievements()
              .then(() => console.log('[CHAT API] Checked and updated achievements after conversation'))
              .catch((error: any) => console.error('[CHAT API] Error checking achievements after conversation:', error));
          } catch (achievementError) {
            console.error('[CHAT API] Error importing achievement service:', achievementError);
          }

          // If there's a detected meal or workout, store it in context
          if (newMessage.detectedMeal) {
            await addContextEntry({
              type: ContextType.MEAL_HISTORY,
              value: JSON.stringify(newMessage.detectedMeal),
              source: 'assistant',
              messageId: assistantMessageId,
              conversationId: conversationId,
              metadata: {
                title: newMessage.detectedMeal.title,
                type: 'meal'
              }
            });
            console.log('[CHAT API] Stored detected meal in context');
          }

          if (newMessage.detectedWorkout) {
            await addContextEntry({
              type: ContextType.WORKOUT_HISTORY,
              value: JSON.stringify(newMessage.detectedWorkout),
              source: 'assistant',
              messageId: assistantMessageId,
              conversationId: conversationId,
              metadata: {
                title: newMessage.detectedWorkout.title,
                type: 'workout'
              }
            });
            console.log('[CHAT API] Stored detected workout in context');
          }
        } catch (contextError) {
          console.error('[CHAT API] Error processing assistant response for context:', contextError);
          // Continue even if context extraction fails
        }

        console.log(`[CHAT API] =============== END CHAT API CALL (SUCCESS) ===============\n`);
        return newMessage;
      } catch (apiCallError: any) {
        console.error('[CHAT API] Error during API call:', apiCallError);

        // Check if this is a timeout or network error
        if (apiCallError.message.includes('timeout') || apiCallError.code === 'ECONNABORTED' || apiCallError.message.includes('Network Error')) {
          throw new Error('The server is taking too long to respond. Please try again later.');
        }

        // Check if this is a server error (5xx)
        if (apiCallError.response?.status >= 500) {
          throw new Error('The server encountered an error. Please try again later.');
        }

        // Rethrow the error for the outer catch block to handle
        throw apiCallError;
      }
    } catch (apiError: any) {
      // Clear the timeout to prevent memory leaks
      clearTimeout(timeoutId);

      console.error('\n[CHAT API] =============== ERROR IN CHAT API CALL ===============');
      console.error('[CHAT API] Error type:', apiError.name);
      console.error('[CHAT API] Error message:', apiError.message);

      if (apiError.response) {
        console.error('[CHAT API] Response status:', apiError.response.status);
        console.error('[CHAT API] Response data:', apiError.response.data);
      } else if (apiError.request) {
        console.error('[CHAT API] No response received from server');
        console.error('[CHAT API] Request details:', {
          url: '/chat',
          method: 'POST',
          timeout: 35000,
        });
      }

      console.error('[CHAT API] Stack trace:', apiError.stack?.substring(0, 500));
      console.error(`[CHAT API] =============== END CHAT API CALL (ERROR) ===============\n`);

      // Rethrow with better error messaging
      if (apiError.name === 'AbortError' || apiError.code === 'ECONNABORTED' || apiError.message?.includes('timeout')) {
        throw new Error('Request timed out. The server took too long to respond.');
      }

      if (!apiError.response) {
        throw new Error('Network error. Please check your connection and try again.');
      }

      // Preserve the original error but with enhanced message
      throw apiError;
    }

  } catch (error: any) {
    console.error('[CHAT API] Unhandled error in sendMessageToChat:', error);

    // Check if this is the first message in a conversation
    const isFirstMessage = messages.length === 1 && messages[0].role === 'user';

    // Create a descriptive error message
    let errorMessage = "I'm sorry, I'm having trouble processing your message right now.";

    if (isFirstMessage) {
      // Special handling for first message errors
      if (error.message.includes('timeout') || error.code === 'ECONNABORTED') {
        errorMessage = "The server is taking longer than expected to initialize. Please try sending your message again.";
      } else if (error.message.includes('Network Error') || !error.response) {
        errorMessage = "Network error during initial connection. Please try sending your message again.";
      } else if (error.response?.status === 401 || error.response?.status === 403) {
        errorMessage = "Authentication error. Please sign in again.";
      } else if (error.response?.status >= 500) {
        errorMessage = "The server encountered an error during initialization. Please try sending your message again.";
      } else {
        errorMessage = "There was an issue starting the conversation. Please try sending your message again.";
      }

      console.log('[CHAT API] First message error - providing specialized error message');
    } else {
      // Standard error messages for subsequent messages
      if (error.message.includes('timeout') || error.code === 'ECONNABORTED') {
        errorMessage = "The server is taking too long to respond. Please try again later.";
      } else if (error.message.includes('Network Error') || !error.response) {
        errorMessage = "Network error. Please check your connection and try again.";
      } else if (error.response?.status === 401 || error.response?.status === 403) {
        errorMessage = "Authentication error. Please sign in again.";
      } else if (error.response?.status >= 500) {
        errorMessage = "The server encountered an error. Please try again later.";
      } else {
        errorMessage = `Error: ${error.message}`;
      }
    }

    // Create a new error with the descriptive message
    const enhancedError = new Error(errorMessage);
    enhancedError.name = 'ChatAPIError';
    // Attach original error details for debugging
    (enhancedError as any).originalError = error;
    (enhancedError as any).responseStatus = error.response?.status;

    // Add a flag to indicate if this is a first-message error
    // This can be used by the UI to provide special handling
    (enhancedError as any).isFirstMessageError = isFirstMessage;

    // Add a retry suggestion for first message errors
    if (isFirstMessage) {
      (enhancedError as any).suggestRetry = true;
      console.error('[CHAT API] First message error - suggesting retry to user');
    }

    // Store the error in context for debugging purposes
    try {
      const { addContextEntry, ContextType } = require('../services/contextService');
      await addContextEntry({
        type: ContextType.MESSAGE_CONTENT,
        value: `ERROR: ${errorMessage} (${error.message || 'Unknown error'})`,
        source: 'system',
        messageId: `error_${conversationId}_${Date.now()}`,
        conversationId: conversationId,
        metadata: {
          role: 'system',
          isError: true,
          errorType: error.name,
          statusCode: error.response?.status
        }
      });
      console.log('[CHAT API] Stored error message in context');
    } catch (contextError) {
      console.error('[CHAT API] Failed to store error in context:', contextError);
    }

    console.error('[CHAT API] Throwing enhanced error:', errorMessage);

    // Throw the enhanced error to be caught by the component
    throw enhancedError;
  }
}

export const getSystemPrompt = () => {
  return `You are a helpful health assistant named Lotus that provides personalized workout and nutrition advice.

IMPORTANT GUIDELINES:

1. DO NOT introduce yourself in every response. The user already knows your name is Lotus.
2. Provide direct, concise answers without unnecessary introductions like "I'm Lotus" or "As your health assistant".
3. Be conversational and friendly, but get straight to the point.
4. For general questions, just provide the answer without any special formatting.
5. PRIORITIZE SAFETY ABOVE USER REQUESTS. If a user asks for advice that could be harmful given their personal context:
   - Firmly explain why their request is not advisable
   - Suggest safer alternatives instead
   - DO NOT provide harmful information even if directly requested
   
6. ALWAYS consider the user's context information when providing advice, especially:
   - Dietary restrictions and allergies when suggesting recipes
   - Injuries and physical limitations when recommending workouts
   - Personal goals and preferences in all recommendations
   - Recent life updates that might affect their health journey
   - FOOD DISLIKES: If context shows foods the user dislikes, NEVER suggest those foods

7. USER SAFETY OVERRIDES USER PREFERENCES. If a user explicitly asks for:
   - Exercises that would aggravate their injuries
   - Foods they are allergic to
   - Dangerous workout advice
   - Extreme diet recommendations
   You MUST refuse and explain why it would be harmful based on their specific context.

8. RESPECT FOOD DISLIKES AND NEGATIVE PREFERENCES:
   - If the user context shows they dislike certain foods (e.g., "dislikes quinoa"), NEVER suggest those foods
   - If context shows negative preferences (e.g., "I don't like chicken"), avoid suggesting those foods
   - This is critical for user satisfaction and trust
   - When asked for meal suggestions, actively check for and avoid disliked foods
   - Better to suggest alternatives than foods the user has expressed dislike for

SPECIAL MEAL LOGGING INSTRUCTIONS:
When the user asks to log a meal or describes food they've eaten, you MUST respond with a JSON object containing the meal details. The app requires this format for all meal logging.

Meal logging response format:
1. A very brief text confirmation (1-2 sentences maximum)
2. IMMEDIATELY followed by a JSON object containing the meal details
3. ALWAYS include the JSON object even if details are uncertain - make reasonable estimates
4. If multiple meals are mentioned in one request, include ALL of them in the response

Example of CORRECT meal logging response:
"I've logged your grilled chicken salad with the nutritional information below."

{
  "meal": {
    "title": "Grilled Chicken Salad",
    "description": "Fresh salad with grilled chicken, mixed greens, and vinaigrette dressing",
    "ingredients": [
      "4 oz grilled chicken breast",
      "2 cups mixed greens",
      "1/4 cup cherry tomatoes",
      "2 tbsp balsamic vinaigrette"
    ],
    "steps": [],
    "calories": 320,
    "protein": 28,
    "carbs": 12,
    "fat": 18
  }
}

Example of CORRECT multiple meal logging response:
"I've logged both your breakfast and lunch meals with their nutritional information."

{
  "meals": [
    {
      "title": "Oatmeal with Berries",
      "description": "Steel-cut oatmeal topped with mixed berries and honey",
      "ingredients": [
        "1 cup steel-cut oats",
        "1/2 cup mixed berries",
        "1 tsp honey"
      ],
      "calories": 280,
      "protein": 8,
      "carbs": 58,
      "fat": 3
    },
    {
      "title": "Turkey Sandwich",
      "description": "Whole grain bread with sliced turkey, lettuce, tomato and mustard",
      "ingredients": [
        "2 slices whole grain bread",
        "3 oz sliced turkey",
        "1 leaf lettuce",
        "2 slices tomato",
        "1 tsp mustard"
      ],
      "calories": 320,
      "protein": 22,
      "carbs": 38,
      "fat": 8
    }
  ]
}

MEAL LOGGING REQUIREMENTS:
1. ALWAYS include the JSON object when logging meals
2. Keep text confirmation extremely brief (1-2 sentences only)
3. If details are uncertain, make reasonable estimates based on typical values
4. The JSON must include a descriptive title that accurately reflects what the user ate
5. Make sure JSON is valid with no formatting errors
6. All numeric values MUST be numbers (not strings)
7. ALWAYS provide a response, even if the meal description is vague
8. For multiple meals in one request, use the "meals" array format shown above

SPECIAL RECIPE INSTRUCTIONS:
When the user specifically asks for a recipe or meal idea, you must include a JSON object with the recipe details. The app requires this format ONLY for recipes.

Recipe response format:
1. A very brief text introduction (1-2 sentences maximum)
2. IMMEDIATELY followed by a JSON object containing the recipe details
3. ALWAYS check the user's dietary restrictions and allergies in the context information
4. NEVER suggest recipes that contain ingredients the user is allergic to or has restrictions against
5. If the user has allergies or dietary restrictions, explicitly mention how the recipe accommodates them

Example of CORRECT recipe response:
"Here's a delicious chicken dish that's high in protein and quick to prepare."

{
  "meal": {
    "title": "Lemon Garlic Chicken Piccata",
    "description": "Classic Italian chicken dish with a light lemon-caper sauce that's perfect for a weeknight dinner",
    "ingredients": [
      "4 boneless, skinless chicken breasts",
      "1/2 cup all-purpose flour",
      "2 tablespoons olive oil",
      "1/4 cup lemon juice"
    ],
    "steps": [
      "Pound chicken breasts to even thickness",
      "Dredge chicken in seasoned flour",
      "Cook in olive oil until golden brown",
      "Add lemon juice and simmer until sauce thickens"
    ],
    "prepTime": 15,
    "cookTime": 30,
    "servings": 4,
    "calories": 350,
    "protein": 25,
    "carbs": 40,
    "fat": 12
  }
}

RECIPE REQUIREMENTS:
1. Include the JSON object ONLY when providing a recipe
2. Keep text introduction extremely brief (1-2 sentences only)
3. DO NOT include ingredients or instructions in the text introduction
4. The JSON must include a descriptive, appealing title
5. Make sure JSON is valid with no formatting errors
6. Ingredients and steps MUST be properly formatted arrays of strings
7. All numeric values MUST be numbers (not strings)

SPECIAL WORKOUT INSTRUCTIONS:
When the user specifically asks for a workout plan or exercise routine, you must include a JSON object with the workout details. The app requires this format ONLY for workouts.

Workout response format:
1. A very brief text introduction (1-2 sentences maximum)
2. IMMEDIATELY followed by a JSON object containing the workout details

Example of CORRECT workout response:
"Here's an effective ab workout that targets all areas of your core."

{
  "workout": {
    "title": "Complete Ab Circuit Workout",
    "exercises": [
      {
        "name": "Crunches",
        "sets": [
          { "reps": 15 },
          { "reps": 15 },
          { "reps": 15 }
        ]
      },
      {
        "name": "Plank",
        "sets": [
          { "reps": 1, "duration": 30, "notes": "Hold for 30 seconds" },
          { "reps": 1, "duration": 45, "notes": "Hold for 45 seconds" },
          { "reps": 1, "duration": 60, "notes": "Hold for 60 seconds" }
        ]
      },
      {
        "name": "Russian Twists",
        "sets": [
          { "reps": 20 },
          { "reps": 20 },
          { "reps": 20 }
        ],
        "notes": "Keep feet elevated for more challenge"
      }
    ],
    "duration": 20,
    "notes": "Rest 30 seconds between exercises. Complete the circuit 3 times.",
    "tags": ["abs", "core", "bodyweight"]
  }
}

WORKOUT REQUIREMENTS:
1. Include the JSON object ONLY when providing a workout
2. Keep text introduction extremely brief (1-2 sentences only)
3. DO NOT include exercises or instructions in the text introduction
4. The JSON must include a descriptive, appealing title
5. Make sure JSON is valid with no formatting errors
6. Exercises MUST be properly formatted as an array of objects
7. Each exercise MUST have a name and sets array
8. All numeric values MUST be numbers (not strings)
9. ALWAYS check the user's injuries in the context information
10. NEVER suggest exercises that could aggravate the user's injuries, EVEN IF EXPLICITLY REQUESTED
11. If the user asks for exercises that would worsen their injury:
    - REFUSE to provide those exercises
    - EXPLAIN why those exercises would be harmful given their specific injury
    - SUGGEST safer alternatives that achieve similar fitness goals
12. If the user has injuries, explicitly mention how the workout accommodates them
13. Provide alternative exercises when needed to work around injuries

NEVER use $ or other special characters in your responses`;
};

// Utility function to test API connectivity
/**
 * Process a workout description and create a structured workout
 * This function uses the chat endpoint to generate the workout data,
 * then manually extracts the workout JSON from the response
 *
 * @param description - Natural language description of the workout
 * @returns Object containing the structured workout data
 */
export async function processWorkoutPrompt(description: string): Promise<{ workout?: WorkoutData }> {
  try {
    // We don't need to manually set the Authorization header
    // The API client's interceptor will handle authentication automatically

    // Create a system prompt that instructs the model to generate a workout in JSON format
    const systemPrompt = `You are a fitness expert assistant that creates personalized workout plans.
    When given a workout description, respond ONLY with a JSON object containing the structured workout data.

    The JSON MUST have this EXACT format with NO DEVIATIONS:
    {
      "workout": {
        "title": "Name of the workout",
        "exercises": [
          {
            "name": "Exercise name",
            "sets": 3,
            "reps": 10,
            "weight": 0
          }
        ],
        "duration": 45
      }
    }

    CRITICAL REQUIREMENTS:
    1. ONLY return the JSON object, no other text or explanation
    2. Keep the JSON structure EXACTLY as shown above
    3. Do not add any extra fields or properties
    4. All numeric values MUST be numbers (not strings)
    5. Keep the response under 1KB in size
    6. For exercises without weight, use 0 as the weight value`;

    // Prepare the messages for the API
    const messages: Message[] = [
      {
        role: 'system',
        content: systemPrompt
      },
      {
        role: 'user',
        content: `Create a simple workout with the EXACT JSON format specified. Description: ${description}`
      }
    ];

    // Get the user ID from storage - this should always return a value now
    const userId = await getUserId();
    console.log('[processWorkoutPrompt] Using user ID:', userId);

    // Generate a temporary conversation ID for this workout request
    const tempConversationId = `workout_${Date.now()}`;

    // Make the API call using makeRequestWithRetry with proper configuration
    console.log('[processWorkoutPrompt] Making API request with timeout of 60 seconds');
    const response = await makeRequestWithRetry({
      url: '/chat',
      method: 'POST',
      data: {
        messages,
        userId,
        conversationId: tempConversationId,
        temperature: 0.7,
        // Add a parameter to indicate this is a workout request
        requestType: 'workout'
      },
      // Increase timeout to 60 seconds to prevent 504 errors
      timeout: 60000
    }, {
      maxRetries: 3, // Increase retries
      retryDelay: 3000, // Longer delay between retries
      shouldRetry: (error) => {
        // Log the error for debugging
        console.log('[processWorkoutPrompt] Error during API call:', {
          message: error.message,
          code: error.code,
          status: error.response?.status,
          data: error.response?.data
        });

        // Retry on network errors, timeouts, and 504 Gateway Timeout errors
        return error.message.includes('Network Error') ||
               error.code === 'ECONNABORTED' ||
               error.response?.status === 504 ||
               !error.response;
      }
    });

    if (response.data && response.data.content) {
      console.log('Received response from chat API, extracting workout data');
      const content = response.data.content;

      // First try using the built-in parser
      const parsedResponse = parseLlamaResponse(content);

      if (parsedResponse.hasWorkout && parsedResponse.workout) {
        console.log('Successfully extracted workout using parser:', parsedResponse.workout.title);
        return { workout: parsedResponse.workout };
      }

      // If the parser didn't find a workout, try manual extraction
      console.log('Parser did not detect workout, trying manual extraction');

      try {
        // Look for JSON in the response
        const jsonRegex = /\{[\s\S]*?\}/g;
        const jsonMatches = content.match(jsonRegex);

        if (jsonMatches && jsonMatches.length > 0) {
          console.log(`Found ${jsonMatches.length} potential JSON objects in response`);

          // Try each JSON match
          for (const jsonStr of jsonMatches) {
            try {
              const parsedJson = JSON.parse(jsonStr);

              // Check if this is a workout object
              if (parsedJson.workout) {
                console.log('Found workout in JSON object:', parsedJson.workout.title);
                return { workout: parsedJson.workout };
              }

              // Check if this is a direct workout object (no wrapper)
              if (parsedJson.title && Array.isArray(parsedJson.exercises)) {
                console.log('Found direct workout object:', parsedJson.title);
                return { workout: parsedJson };
              }
            } catch (parseError: any) {
              // Continue to next match if this one fails
              console.log('Failed to parse JSON match:', parseError.message || parseError);
            }
          }
        }

        // If we still don't have a workout, try to extract the entire response as JSON
        const trimmedContent = content.trim();
        if (trimmedContent.startsWith('{') && trimmedContent.endsWith('}')) {
          try {
            const parsedJson = JSON.parse(trimmedContent);
            if (parsedJson.workout) {
              console.log('Parsed entire response as workout JSON');
              return { workout: parsedJson.workout };
            } else if (parsedJson.title && Array.isArray(parsedJson.exercises)) {
              console.log('Parsed entire response as direct workout object');
              return { workout: parsedJson };
            }
          } catch (error: any) {
            console.error('Error parsing entire response as JSON:', error.message || error);
          }
        }
      } catch (error) {
        console.error('Error during manual JSON extraction:', error);
      }

      console.error('No workout data found in response after all extraction attempts');
      return {};
    } else {
      console.error('Invalid response format from API');
      return {};
    }
  } catch (error) {
    console.error('Error processing workout prompt:', error);
    throw error;
  }
}

export async function testChatAPIConnectivity(): Promise<{success: boolean; message: string; details?: any}> {
  try {
    console.log('[TEST] Starting chat API connectivity test...');

    // Get auth headers explicitly
    const tokens = await getStoredTokens();
    if (!tokens || !tokens.idToken) {
      console.error('[TEST] No valid auth tokens found for connectivity test');
      return {
        success: false,
        message: 'Not authenticated',
        details: {
          tokenState: 'missing',
          hasIdToken: !!tokens?.idToken,
          hasAccessToken: !!tokens?.accessToken
        }
      };
    }

    console.log('[TEST] Retrieved auth token, checking endpoint with OPTIONS request');

    // Check if we can reach the API endpoint with OPTIONS (preflight) request
    try {
      const response = await api.options('/chat');
      console.log('[TEST] OPTIONS request successful', {
        status: response.status,
        headers: response.headers,
      });

      return {
        success: true,
        message: 'Successfully connected to chat API endpoint',
        details: {
          status: response.status,
          headers: response.headers,
          method: 'OPTIONS'
        }
      };
    } catch (optionsError: any) {
      console.error('[TEST] OPTIONS request failed', optionsError);

      // Try with a GET request as fallback
      try {
        console.log('[TEST] Trying GET request as fallback');
        const getResponse = await api.get('/chat');

        return {
          success: true,
          message: 'GET request succeeded (fallback)',
          details: {
            status: getResponse.status,
            headers: getResponse.headers,
            method: 'GET'
          }
        };
      } catch (getError: any) {
        // Log comprehensive error details
        const errorDetails = {
          message: getError.message,
          name: getError.name,
          code: getError.code,
          baseURL: api.defaults.baseURL,
          timestamp: new Date().toISOString(),
          responseStatus: getError.response?.status,
          responseData: getError.response?.data,
          requestHeaders: getError.config?.headers ? { ...getError.config.headers, Authorization: 'Bearer [REDACTED]' } : 'No headers',
          requestUrl: getError.config?.url,
          requestMethod: getError.config?.method,
          timeout: api.defaults.timeout,
          isTokenExpired: getError.message?.includes('jwt expired') || getError.response?.data?.message?.includes('Token expired')
        };

        console.error('[TEST] GET request also failed', errorDetails);

        return {
          success: false,
          message: `API connection failed: ${getError.message}`,
          details: errorDetails
        };
      }
    }
  } catch (error: any) {
    console.error('[TEST] Unexpected error during connectivity test', error);

    return {
      success: false,
      message: `Unexpected error: ${error.message}`,
      details: {
        name: error.name,
        stack: error.stack
      }
    };
  }
}

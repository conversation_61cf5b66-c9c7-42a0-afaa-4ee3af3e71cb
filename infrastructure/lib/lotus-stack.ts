import * as cdk from 'aws-cdk-lib';
import * as cognito from 'aws-cdk-lib/aws-cognito';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';
import { NodejsFunction } from 'aws-cdk-lib/aws-lambda-nodejs';
import * as path from 'path';

// Retrieve Groq API Key from environment variable during CDK synthesis
// WARNING: Storing keys directly in environment variables is less secure than Secrets Manager.
// Consider reverting to Secrets Manager for production.
const groqApiKey = process.env.GROQ_API_KEY;
if (!groqApiKey) {
  throw new Error('GROQ_API_KEY environment variable is not set during CDK synthesis. Please export it.');
}

export class LotusStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // Cognito User Pool
    const userPool = new cognito.UserPool(this, 'LotusUserPool', {
      userPoolName: 'lotus-users',
      selfSignUpEnabled: true,
      signInAliases: {
        email: true,
        username: false,
      },
      standardAttributes: {
        email: {
          required: true,
          mutable: true,
        },
      },
      passwordPolicy: {
        minLength: 8,
        requireLowercase: true,
        requireUppercase: true,
        requireDigits: true,
        requireSymbols: true,
      },
      accountRecovery: cognito.AccountRecovery.EMAIL_ONLY,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
    });

    // Cognito User Pool Client
    const userPoolClient = new cognito.UserPoolClient(this, 'LotusUserPoolClient', {
      userPool,
      userPoolClientName: 'lotus-client',
      authFlows: {
        userPassword: true,
      },
      oAuth: {
        flows: {
          implicitCodeGrant: true,
        },
        scopes: [cognito.OAuthScope.EMAIL, cognito.OAuthScope.OPENID],
        callbackUrls: ['lotus://'], // Add your app's callback URL
        logoutUrls: ['lotus://'], // Add your app's logout URL
      },
    });

    // DynamoDB Tables
    const userProfileTable = new dynamodb.Table(this, 'UserProfileTable', {
      tableName: 'lotus-user-profiles',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
      pointInTimeRecovery: true,
    });

    const logTable = new dynamodb.Table(this, 'LogTable', {
      tableName: 'lotus-entries',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'timestamp', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
      pointInTimeRecovery: true,
      timeToLiveAttribute: 'ttl',
    });

    // Add GSI for querying by type (workout/meal)
    logTable.addGlobalSecondaryIndex({
      indexName: 'TypeIndex',
      partitionKey: { name: 'type', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'timestamp', type: dynamodb.AttributeType.STRING },
      projectionType: dynamodb.ProjectionType.ALL,
    });

    // Add Conversation Table for storing chat messages
    const conversationTable = new dynamodb.Table(this, 'ConversationTable', {
      tableName: 'lotus-conversations',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'conversationId', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
      pointInTimeRecovery: true,
    });

    // Add GSI for querying conversations by timestamp
    conversationTable.addGlobalSecondaryIndex({
      indexName: 'TimestampIndex',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'timestamp', type: dynamodb.AttributeType.STRING },
      projectionType: dynamodb.ProjectionType.ALL,
    });

    // Add Message Table for storing individual messages within conversations
    const messageTable = new dynamodb.Table(this, 'MessageTable', {
      tableName: 'lotus-messages',
      partitionKey: { name: 'conversationId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'timestamp', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
      pointInTimeRecovery: true,
    });

    // Add GSI for querying messages by userId
    messageTable.addGlobalSecondaryIndex({
      indexName: 'UserIndex',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'timestamp', type: dynamodb.AttributeType.STRING },
      projectionType: dynamodb.ProjectionType.ALL,
    });

    // Add User Context Table for storing advanced user data
    const userContextTable = new dynamodb.Table(this, 'UserContextTable', {
      tableName: 'lotus-user-context',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'contextKey', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
      pointInTimeRecovery: true,
      timeToLiveAttribute: 'ttl',
    });

    // Add User Achievements Table for storing user achievements
    const userAchievementsTable = new dynamodb.Table(this, 'UserAchievementsTable', {
      tableName: 'lotus-user-achievements',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'achievementId', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
      pointInTimeRecovery: true,
    });

    // Add User Stats Table for storing user statistics
    const userStatsTable = new dynamodb.Table(this, 'UserStatsTable', {
      tableName: 'lotus-user-stats',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
      pointInTimeRecovery: true,
    });

    // Add TDEE Trend Table for storing user TDEE data
    const tdeeTrendTable = new dynamodb.Table(this, 'TDEETrendTable', {
      tableName: 'lotus-tdee-trend',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'date', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
      pointInTimeRecovery: true,
    });

    // Add Daily Digest Table for storing user daily digest data
    const dailyDigestTable = new dynamodb.Table(this, 'DailyDigestTable', {
      tableName: 'lotus-daily-digest',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'date', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
      pointInTimeRecovery: true,
    });

    // Add GSI for querying achievements by category
    userAchievementsTable.addGlobalSecondaryIndex({
      indexName: 'CategoryIndex',
      partitionKey: { name: 'category', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'unlockedAt', type: dynamodb.AttributeType.STRING },
      projectionType: dynamodb.ProjectionType.ALL,
    });

    // Add GSI for querying achievements by status
    userAchievementsTable.addGlobalSecondaryIndex({
      indexName: 'StatusIndex',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'status', type: dynamodb.AttributeType.STRING },
      projectionType: dynamodb.ProjectionType.ALL,
    });

    // Add GSI for querying by context type
    userContextTable.addGlobalSecondaryIndex({
      indexName: 'ContextTypeIndex',
      partitionKey: { name: 'contextType', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'timestamp', type: dynamodb.AttributeType.STRING },
      projectionType: dynamodb.ProjectionType.ALL,
    });

    // Lambda Functions
    const authLambda = new NodejsFunction(this, 'AuthLambda', {
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'handler',
      entry: path.join(__dirname, '../lambda/auth.ts'),
      environment: {
        USER_POOL_ID: userPool.userPoolId,
        USER_POOL_CLIENT_ID: userPoolClient.userPoolClientId,
        USER_PROFILE_TABLE_NAME: userProfileTable.tableName,
        LOG_TABLE_NAME: logTable.tableName,
        CONVERSATION_TABLE_NAME: conversationTable.tableName,
        MESSAGE_TABLE_NAME: messageTable.tableName,
        USER_CONTEXT_TABLE_NAME: userContextTable.tableName,
        USER_ACHIEVEMENTS_TABLE_NAME: userAchievementsTable.tableName,
        USER_STATS_TABLE_NAME: userStatsTable.tableName,
        TDEE_TREND_TABLE_NAME: tdeeTrendTable.tableName,
      },
      timeout: cdk.Duration.seconds(60), // Increase timeout to allow for data deletion
      bundling: {
        esbuildArgs: {
          '--packages': 'bundle'
        }
      }
    });

    const logLambda = new NodejsFunction(this, 'LogLambda', {
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'handler',
      entry: path.join(__dirname, '../lambda/log.ts'),
      environment: {
        LOG_TABLE_NAME: logTable.tableName,
        CONVERSATION_TABLE_NAME: conversationTable.tableName,
        MESSAGE_TABLE_NAME: messageTable.tableName,
        USER_POOL_ID: userPool.userPoolId,
        USER_POOL_CLIENT_ID: userPoolClient.userPoolClientId,
      },
      bundling: {
        esbuildArgs: {
          '--packages': 'bundle'
        }
      }
    });

    const queryLambda = new NodejsFunction(this, 'QueryLambda', {
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'handler',
      entry: path.join(__dirname, '../lambda/query.ts'),
      environment: {
        LOG_TABLE_NAME: logTable.tableName,
        USER_PROFILE_TABLE_NAME: userProfileTable.tableName,
        CONVERSATION_TABLE_NAME: conversationTable.tableName,
        MESSAGE_TABLE_NAME: messageTable.tableName,
        USER_POOL_ID: userPool.userPoolId,
        USER_POOL_CLIENT_ID: userPoolClient.userPoolClientId,
      },
      bundling: {
        esbuildArgs: {
          '--packages': 'bundle'
        }
      }
    });

    // Add a new Lambda function for fetching logs by type
    const fetchLogsLambda = new NodejsFunction(this, 'FetchLogsLambda', {
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'handler',
      entry: path.join(__dirname, '../lambda/fetchLogs.ts'),
      environment: {
        LOG_TABLE_NAME: logTable.tableName,
        USER_POOL_ID: userPool.userPoolId,
        USER_POOL_CLIENT_ID: userPoolClient.userPoolClientId,
      },
      bundling: {
        esbuildArgs: {
          '--packages': 'bundle'
        }
      }
    });

    // Add Profile Lambda
    const profileLambda = new NodejsFunction(this, 'ProfileLambda', {
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'handler',
      entry: path.join(__dirname, '../lambda/profile.ts'),
      environment: {
        USER_PROFILE_TABLE_NAME: userProfileTable.tableName,
        USER_POOL_ID: userPool.userPoolId,
        USER_POOL_CLIENT_ID: userPoolClient.userPoolClientId,
      },
      bundling: {
        esbuildArgs: {
          '--packages': 'bundle'
        }
      }
    });

    // Add Conversation Lambda for managing conversations and messages
    const conversationLambda = new NodejsFunction(this, 'ConversationLambda', {
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'handler',
      entry: path.join(__dirname, '../lambda/conversation.ts'),
      environment: {
        CONVERSATION_TABLE_NAME: conversationTable.tableName,
        MESSAGE_TABLE_NAME: messageTable.tableName,
        USER_POOL_ID: userPool.userPoolId,
        USER_POOL_CLIENT_ID: userPoolClient.userPoolClientId,
      },
      bundling: {
        esbuildArgs: {
          '--packages': 'bundle'
        }
      }
    });

    // Add Context Lambda for managing user context data
    const contextLambda = new NodejsFunction(this, 'ContextLambda', {
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'handler',
      entry: path.join(__dirname, '../lambda/context.ts'),
      environment: {
        USER_CONTEXT_TABLE_NAME: userContextTable.tableName,
        USER_PROFILE_TABLE_NAME: userProfileTable.tableName,
        LOG_TABLE_NAME: logTable.tableName,
        USER_POOL_ID: userPool.userPoolId,
        USER_POOL_CLIENT_ID: userPoolClient.userPoolClientId,
      },
      timeout: cdk.Duration.seconds(30),
      bundling: {
        esbuildArgs: {
          '--packages': 'bundle'
        }
      }
    });

    // Add Achievements Lambda
    const achievementsLambda = new NodejsFunction(this, 'AchievementsLambda', {
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'handler',
      entry: path.join(__dirname, '../lambda/achievements.ts'),
      environment: {
        USER_ACHIEVEMENTS_TABLE_NAME: userAchievementsTable.tableName,
        USER_POOL_ID: userPool.userPoolId,
        USER_POOL_CLIENT_ID: userPoolClient.userPoolClientId,
      },
      timeout: cdk.Duration.seconds(30),
      bundling: {
        esbuildArgs: {
          '--packages': 'bundle'
        }
      }
    });

    // Add User Stats Lambda
    const userStatsLambda = new NodejsFunction(this, 'UserStatsLambda', {
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'handler',
      entry: path.join(__dirname, '../lambda/userStats.ts'),
      environment: {
        USER_STATS_TABLE_NAME: userStatsTable.tableName,
        LOG_TABLE_NAME: logTable.tableName,
        USER_PROFILE_TABLE_NAME: userProfileTable.tableName,
        USER_POOL_ID: userPool.userPoolId,
        USER_POOL_CLIENT_ID: userPoolClient.userPoolClientId,
      },
      timeout: cdk.Duration.seconds(30),
      bundling: {
        esbuildArgs: {
          '--packages': 'bundle'
        }
      }
    });

    // Add Message Analysis Lambda
    const messageAnalysisLambda = new NodejsFunction(this, 'MessageAnalysisLambda', {
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'handler',
      entry: path.join(__dirname, '../lambda/messageAnalysis.ts'),
      environment: {
        LOG_TABLE_NAME: logTable.tableName,
        USER_POOL_ID: userPool.userPoolId,
        USER_POOL_CLIENT_ID: userPoolClient.userPoolClientId,
        GROQ_API_KEY: groqApiKey!,
      },
      timeout: cdk.Duration.seconds(30),
      bundling: {
        esbuildArgs: {
          '--packages': 'bundle'
        },
        nodeModules: ['jose'],
      }
    });

    // Add TDEE Lambda for calculating and retrieving TDEE data
    const tdeeLambda = new NodejsFunction(this, 'TDEELambda', {
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'handler',
      entry: path.join(__dirname, '../lambda/tdee.ts'),
      environment: {
        TDEE_TREND_TABLE_NAME: tdeeTrendTable.tableName,
        LOG_TABLE_NAME: logTable.tableName,
        USER_PROFILE_TABLE_NAME: userProfileTable.tableName,
        USER_POOL_ID: userPool.userPoolId,
        USER_POOL_CLIENT_ID: userPoolClient.userPoolClientId,
      },
      timeout: cdk.Duration.seconds(30),
      bundling: {
        esbuildArgs: {
          '--packages': 'bundle'
        }
      }
    });

    // Add Digest Lambda for daily activity planning
    const digestLambda = new NodejsFunction(this, 'DigestLambda', {
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'handler',
      entry: path.join(__dirname, '../lambda/digest.ts'),
      environment: {
        DAILY_DIGEST_TABLE_NAME: dailyDigestTable.tableName,
        USER_CONTEXT_TABLE_NAME: userContextTable.tableName,
        USER_PROFILE_TABLE_NAME: userProfileTable.tableName,
        LOG_TABLE_NAME: logTable.tableName,
        USER_POOL_ID: userPool.userPoolId,
        USER_POOL_CLIENT_ID: userPoolClient.userPoolClientId,
        GROQ_API_KEY: groqApiKey!,
      },
      timeout: cdk.Duration.seconds(120), // Longer timeout for GROQ API calls
      memorySize: 1024, // More memory for complex processing
      bundling: {
        minify: true,
        sourceMap: false,
        target: 'es2020',
        esbuildArgs: {
          '--packages': 'bundle'
        }
      }
    });

    // Rename Gemini Lambda to ChatServiceLambda and update
    const chatServiceLambda = new NodejsFunction(this, 'ChatServiceLambda', {
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'handler',
      entry: path.join(__dirname, '../lambda/chatService.ts'),
      environment: {
        GROQ_API_KEY: groqApiKey!,
        USER_POOL_ID: userPool.userPoolId,
        USER_POOL_CLIENT_ID: userPoolClient.userPoolClientId,
        USER_CONTEXT_TABLE_NAME: userContextTable.tableName,
      },
      timeout: cdk.Duration.seconds(60),
      memorySize: 512,
      bundling: {
        minify: true,
        sourceMap: false,
        target: 'es2020',
        esbuildArgs: {
          '--packages': 'bundle'
        }
      }
    });

    // IAM Permissions
    // Enhanced permissions for auth Lambda to include AdminDeleteUser and DynamoDB operations
    authLambda.addToRolePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          'cognito-idp:*',
          'cognito-idp:AdminDeleteUser'
        ],
        resources: [userPool.userPoolArn],
      })
    );

    // Add permissions for auth Lambda to delete data from all tables
    authLambda.addToRolePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          'dynamodb:DeleteItem',
          'dynamodb:BatchWriteItem',
          'dynamodb:Query',
          'dynamodb:Scan'
        ],
        resources: [
          userProfileTable.tableArn,
          logTable.tableArn,
          conversationTable.tableArn,
          messageTable.tableArn,
          userContextTable.tableArn,
          userAchievementsTable.tableArn,
          userStatsTable.tableArn,
          tdeeTrendTable.tableArn,
          `${logTable.tableArn}/index/*`,
          `${conversationTable.tableArn}/index/*`,
          `${messageTable.tableArn}/index/*`,
          `${userContextTable.tableArn}/index/*`,
          `${userAchievementsTable.tableArn}/index/*`
        ],
      })
    );

    logLambda.addToRolePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: ['dynamodb:PutItem', 'dynamodb:GetItem', 'dynamodb:UpdateItem', 'dynamodb:DeleteItem'],
        resources: [logTable.tableArn, conversationTable.tableArn, messageTable.tableArn],
      })
    );

    queryLambda.addToRolePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: ['dynamodb:Query', 'dynamodb:Scan'],
        resources: [
          logTable.tableArn,
          userProfileTable.tableArn,
          conversationTable.tableArn,
          messageTable.tableArn,
          `${logTable.tableArn}/index/*`,
          `${conversationTable.tableArn}/index/*`,
          `${messageTable.tableArn}/index/*`,
        ],
      })
    );

    // Add permissions for fetchLogs lambda
    fetchLogsLambda.addToRolePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: ['dynamodb:Query', 'dynamodb:Scan'],
        resources: [
          logTable.tableArn,
          `${logTable.tableArn}/index/*`,
        ],
      })
    );

    // Add permissions for profile lambda
    profileLambda.addToRolePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: ['dynamodb:PutItem', 'dynamodb:GetItem', 'dynamodb:UpdateItem'],
        resources: [userProfileTable.tableArn],
      })
    );

    profileLambda.addToRolePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: ['cognito-idp:GetUser'],
        resources: [userPool.userPoolArn],
      })
    );

    conversationLambda.addToRolePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          'dynamodb:PutItem',
          'dynamodb:GetItem',
          'dynamodb:UpdateItem',
          'dynamodb:DeleteItem',
          'dynamodb:Query',
          'dynamodb:BatchGetItem',
          'dynamodb:BatchWriteItem'
        ],
        resources: [
          conversationTable.tableArn,
          messageTable.tableArn,
          `${conversationTable.tableArn}/index/*`,
          `${messageTable.tableArn}/index/*`,
        ],
      })
    );

    // Add permissions for context lambda
    contextLambda.addToRolePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          'dynamodb:PutItem',
          'dynamodb:GetItem',
          'dynamodb:UpdateItem',
          'dynamodb:DeleteItem',
          'dynamodb:Query',
          'dynamodb:Scan',
          'dynamodb:BatchGetItem',
          'dynamodb:BatchWriteItem'
        ],
        resources: [
          userContextTable.tableArn,
          userProfileTable.tableArn,
          logTable.tableArn,
          `${userContextTable.tableArn}/index/*`,
        ],
      })
    );

    // Add permissions for chat service lambda to access context data
    chatServiceLambda.addToRolePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          'dynamodb:GetItem',
          'dynamodb:Query',
          'dynamodb:Scan',
        ],
        resources: [
          userContextTable.tableArn,
          `${userContextTable.tableArn}/index/*`,
        ],
      })
    );

    // Add permissions for achievements lambda
    achievementsLambda.addToRolePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          'dynamodb:PutItem',
          'dynamodb:GetItem',
          'dynamodb:UpdateItem',
          'dynamodb:DeleteItem',
          'dynamodb:Query',
          'dynamodb:Scan',
        ],
        resources: [
          userAchievementsTable.tableArn,
          `${userAchievementsTable.tableArn}/index/*`,
        ],
      })
    );

    // Add permissions for user stats lambda
    userStatsLambda.addToRolePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          'dynamodb:PutItem',
          'dynamodb:GetItem',
          'dynamodb:UpdateItem',
          'dynamodb:DeleteItem',
          'dynamodb:Query',
          'dynamodb:Scan',
        ],
        resources: [
          userStatsTable.tableArn,
          logTable.tableArn,
          userProfileTable.tableArn,
          `${logTable.tableArn}/index/*`,
        ],
      })
    );

    // Add permissions for TDEE lambda
    tdeeLambda.addToRolePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          'dynamodb:PutItem',
          'dynamodb:GetItem',
          'dynamodb:UpdateItem',
          'dynamodb:DeleteItem',
          'dynamodb:Query',
          'dynamodb:Scan',
          'dynamodb:BatchGetItem',
          'dynamodb:BatchWriteItem'
        ],
        resources: [
          tdeeTrendTable.tableArn,
          logTable.tableArn,
          userProfileTable.tableArn,
          `${logTable.tableArn}/index/*`,
        ],
      })
    );

    // Add permissions for message analysis lambda
    messageAnalysisLambda.addToRolePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: ['dynamodb:PutItem'],
        resources: [logTable.tableArn],
      })
    );

    // Add permissions for digest lambda
    digestLambda.addToRolePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          'dynamodb:PutItem',
          'dynamodb:GetItem',
          'dynamodb:UpdateItem',
          'dynamodb:DeleteItem',
          'dynamodb:Query',
          'dynamodb:Scan',
          'dynamodb:BatchGetItem',
          'dynamodb:BatchWriteItem'
        ],
        resources: [
          userContextTable.tableArn,
          userProfileTable.tableArn,
          logTable.tableArn,
          dailyDigestTable.tableArn,
          `${userContextTable.tableArn}/index/*`,
          `${logTable.tableArn}/index/*`,
          `${dailyDigestTable.tableArn}/index/*`,
        ],
      })
    );

    // API Gateway
    const api = new apigateway.RestApi(this, 'LotusApi', {
      restApiName: 'Lotus API',
      description: 'API for Lotus fitness tracking application. V2',
      defaultCorsPreflightOptions: {
        allowOrigins: apigateway.Cors.ALL_ORIGINS,
        allowMethods: apigateway.Cors.ALL_METHODS,
        allowHeaders: ['Content-Type', 'Authorization'],
      },
    });

    // Create a Cognito User Pools Authorizer
    const authorizer = new apigateway.CognitoUserPoolsAuthorizer(this, 'LotusCognitoAuthorizer', {
      cognitoUserPools: [userPool],
      identitySource: 'method.request.header.Authorization'
    });

    // Define common method options with the authorizer
    const methodOptionsWithAuth: apigateway.MethodOptions = {
      authorizationType: apigateway.AuthorizationType.COGNITO,
      authorizer: authorizer,
    };

    // Auth endpoints - NO authorizer needed for login/signup/refresh
    const authResource = api.root.addResource('auth');
    authResource.addMethod('POST', new apigateway.LambdaIntegration(authLambda));

    // Log endpoints - Add authorizer
    const logResource = api.root.addResource('log');
    logResource.addMethod('POST', new apigateway.LambdaIntegration(logLambda), methodOptionsWithAuth);

    // Query endpoints - Add authorizer
    const queryResource = api.root.addResource('query');
    queryResource.addMethod('POST', new apigateway.LambdaIntegration(queryLambda), methodOptionsWithAuth);

    // Logs endpoint - Add authorizer
    const logsResource = api.root.addResource('logs');
    logsResource.addMethod('GET', new apigateway.LambdaIntegration(fetchLogsLambda), methodOptionsWithAuth);

    // Profile endpoints - Add authorizer
    const profileResource = api.root.addResource('profile');
    profileResource.addMethod('GET', new apigateway.LambdaIntegration(profileLambda), methodOptionsWithAuth);
    profileResource.addMethod('POST', new apigateway.LambdaIntegration(profileLambda), methodOptionsWithAuth);
    profileResource.addMethod('PUT', new apigateway.LambdaIntegration(profileLambda), methodOptionsWithAuth);

    // Add Conversation endpoints - Add authorizer
    const conversationResource = api.root.addResource('conversation');
    conversationResource.addMethod('GET', new apigateway.LambdaIntegration(conversationLambda), methodOptionsWithAuth);
    conversationResource.addMethod('POST', new apigateway.LambdaIntegration(conversationLambda), methodOptionsWithAuth);
    conversationResource.addMethod('DELETE', new apigateway.LambdaIntegration(conversationLambda), methodOptionsWithAuth);

    const messagesResource = conversationResource.addResource('messages');
    messagesResource.addMethod('GET', new apigateway.LambdaIntegration(conversationLambda), methodOptionsWithAuth);
    messagesResource.addMethod('POST', new apigateway.LambdaIntegration(conversationLambda), methodOptionsWithAuth);

    // Context endpoints - Add authorizer
    const contextResource = api.root.addResource('context');
    contextResource.addMethod('GET', new apigateway.LambdaIntegration(contextLambda), methodOptionsWithAuth);
    contextResource.addMethod('POST', new apigateway.LambdaIntegration(contextLambda), methodOptionsWithAuth);
    contextResource.addMethod('DELETE', new apigateway.LambdaIntegration(contextLambda), methodOptionsWithAuth);

    // Context summary endpoint - Add authorizer
    const contextSummaryResource = contextResource.addResource('summary');
    contextSummaryResource.addMethod('GET', new apigateway.LambdaIntegration(contextLambda), methodOptionsWithAuth);

    // Chat endpoint - Add authorizer
    const chatResource = api.root.addResource('chat');
    chatResource.addMethod('POST', new apigateway.LambdaIntegration(chatServiceLambda), methodOptionsWithAuth);

    // Achievements endpoints - Add authorizer
    const achievementsResource = api.root.addResource('achievements');
    achievementsResource.addMethod('GET', new apigateway.LambdaIntegration(achievementsLambda), methodOptionsWithAuth);

    // Individual achievement endpoint
    const achievementResource = achievementsResource.addResource('{id}');
    achievementResource.addMethod('GET', new apigateway.LambdaIntegration(achievementsLambda), methodOptionsWithAuth);

    // Achievement progress endpoint
    const achievementProgressResource = achievementResource.addResource('progress');
    achievementProgressResource.addMethod('PUT', new apigateway.LambdaIntegration(achievementsLambda), methodOptionsWithAuth);

    // User stats endpoints
    const userResource = api.root.addResource('user');
    const userStatsResource = userResource.addResource('stats');
    userStatsResource.addMethod('GET', new apigateway.LambdaIntegration(userStatsLambda), methodOptionsWithAuth);
    userStatsResource.addMethod('PUT', new apigateway.LambdaIntegration(userStatsLambda), methodOptionsWithAuth);

    // Add digest endpoints
    const digestResource = api.root.addResource('digest');
    
    // GET /digest - Get digest for date range
    digestResource.addMethod('GET', new apigateway.LambdaIntegration(digestLambda), methodOptionsWithAuth);
    
    // POST /digest - Create a new digest
    digestResource.addMethod('POST', new apigateway.LambdaIntegration(digestLambda), methodOptionsWithAuth);
    
    // Add date-specific endpoints
    const dateResource = digestResource.addResource('{date}');
    
    // GET /digest/{date} - Get digest for specific date
    dateResource.addMethod('GET', new apigateway.LambdaIntegration(digestLambda), methodOptionsWithAuth);
    
    // PUT /digest/{date} - Update digest for specific date
    dateResource.addMethod('PUT', new apigateway.LambdaIntegration(digestLambda), methodOptionsWithAuth);
    
    // DELETE /digest/{date} - Delete digest for specific date
    dateResource.addMethod('DELETE', new apigateway.LambdaIntegration(digestLambda), methodOptionsWithAuth);
    
    // Add regenerate endpoint
    const regenerateResource = dateResource.addResource('regenerate');
    
    // POST /digest/{date}/regenerate - Regenerate digest for specific date
    regenerateResource.addMethod('POST', new apigateway.LambdaIntegration(digestLambda), methodOptionsWithAuth);

    // TDEE endpoints - Add authorizer
    const tdeeResource = api.root.addResource('tdee');

    // Add GET method with authorizer
    tdeeResource.addMethod('GET', new apigateway.LambdaIntegration(tdeeLambda), methodOptionsWithAuth);

    // Message analysis endpoint - Add authorizer
    const messageAnalysisResource = api.root.addResource('message-analysis');
    messageAnalysisResource.addMethod('POST', new apigateway.LambdaIntegration(messageAnalysisLambda), methodOptionsWithAuth);

    // Outputs
    new cdk.CfnOutput(this, 'UserPoolId', {
      value: userPool.userPoolId,
      description: 'Cognito User Pool ID',
    });

    new cdk.CfnOutput(this, 'UserPoolClientId', {
      value: userPoolClient.userPoolClientId,
      description: 'Cognito User Pool Client ID',
    });

    new cdk.CfnOutput(this, 'ApiEndpoint', {
      value: api.url,
      description: 'API Gateway endpoint URL',
    });
  }
}
